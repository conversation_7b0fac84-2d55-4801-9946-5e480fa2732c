"""
Main application entry point for Demo Wallet Bot v2
"""

from __future__ import annotations

import asyncio
import logging
import signal
import sys
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from aiogram import Bo<PERSON>, Dispatcher
from aiogram.client.default import DefaultBotProperties
from aiogram.enums import ParseMode

from config.settings import get_settings
from database.connection import init_database, close_database
from handlers import setup_handlers
from middleware import setup_middleware
from utils.logging import setup_logging
from config.logging_config import setup_api_logging

logger = logging.getLogger(__name__)


class BotApplication:
    """Main bot application class"""

    def __init__(self):
        self.settings = get_settings()
        self.bot: Bot | None = None
        self.dp: Dispatcher | None = None
        self._shutdown_event = asyncio.Event()

    async def setup(self) -> None:
        """Initialize bot application"""
        try:
            # Setup logging (console colored, optional rotating file)
            setup_logging(
                level=self.settings.LOG_LEVEL,
                log_file=(
                    self.settings.LOG_FILE_PATH if self.settings.LOG_TO_FILE else None
                ),
                max_file_size=self.settings.LOG_MAX_SIZE,
                backup_count=self.settings.LOG_BACKUP_COUNT,
                enable_security_filter=True,
                structured_format=self.settings.LOG_STRUCTURED,
                environment=self.settings.ENVIRONMENT,
                console_colored=self.settings.LOG_COLOR,
                show_category=self.settings.LOG_SHOW_CATEGORY,
            )
            logger.info("Starting Demo Wallet Bot v2...")

            # Setup enhanced API logging for 403 error diagnosis
            api_loggers = setup_api_logging(
                log_level="DEBUG", enable_console=True, enable_structured=True
            )
            logger.info(
                f"Enhanced API logging initialized with {len(api_loggers)} specialized loggers"
            )

            # Validate bot token
            await self._validate_bot_token()

            # Initialize database
            await init_database()
            logger.info("Database connection established")

            # Create bot and dispatcher
            self.bot = Bot(
                token=self.settings.BOT_TOKEN,
                default=DefaultBotProperties(parse_mode=ParseMode.HTML),
            )
            self.dp = Dispatcher()

            # Setup middleware and handlers
            setup_middleware(self.dp)
            setup_handlers(self.dp)

            # Setup metrics if enabled
            if self.settings.METRICS_ENABLED:
                await self._setup_metrics()

            logger.info("Bot application setup completed")

        except Exception as e:
            logger.error(f"Failed to setup bot application: {e}")
            raise

    async def _validate_bot_token(self) -> None:
        """Validate Telegram bot token format"""
        token = self.settings.BOT_TOKEN.strip()
        if not token or ":" not in token:
            raise ValueError(
                "BOT_TOKEN is missing or invalid. Set it in .env (format: <digits>:<secret>)."
            )

        prefix = token.split(":", 1)[0]
        if not prefix.isdigit():
            raise ValueError("BOT_TOKEN format invalid (should start with digits:...).")

        logger.info("Bot token validation passed")

    async def _setup_metrics(self) -> None:
        """Setup Prometheus metrics server"""
        try:
            from prometheus_client import start_http_server

            start_http_server(self.settings.METRICS_PORT)
            logger.info(f"Metrics server started on port {self.settings.METRICS_PORT}")
        except Exception as e:
            logger.warning(f"Failed to start metrics server: {e}")

    async def _setup_signal_handlers(self) -> None:
        """Setup signal handlers for graceful shutdown.

        Prefer asyncio's loop.add_signal_handler on POSIX so Ctrl+C triggers
        a clean shutdown without hanging.
        """

        def trigger_shutdown() -> None:
            if not self._shutdown_event.is_set():
                self._shutdown_event.set()

        if sys.platform != "win32":
            try:
                loop = asyncio.get_running_loop()
                if hasattr(loop, "add_signal_handler"):
                    loop.add_signal_handler(signal.SIGINT, trigger_shutdown)
                    if hasattr(signal, "SIGTERM"):
                        loop.add_signal_handler(signal.SIGTERM, trigger_shutdown)
                    return
            except Exception:
                # Fallback to sync signal handlers if loop registration fails
                pass

            # Fallback: register sync handlers (best-effort)
            try:
                signal.signal(signal.SIGINT, lambda s, f: trigger_shutdown())
            except Exception:
                pass
            try:
                if hasattr(signal, "SIGTERM"):
                    signal.signal(signal.SIGTERM, lambda s, f: trigger_shutdown())
            except Exception:
                pass

    async def run(self) -> None:
        """Run the bot application"""
        if not self.bot or not self.dp:
            raise RuntimeError("Bot not setup. Call setup() first.")

        try:
            # Setup signal handlers
            await self._setup_signal_handlers()

            # Start retention worker if enabled
            if self.settings.RETENTION_ENABLED:
                logger.info("Starting retention worker...")
                asyncio.create_task(self._retention_worker())
            else:
                logger.info("Retention worker disabled")

            # Start API health monitoring and background tasks
            try:
                from services.background_tasks import start_background_tasks

                await start_background_tasks()
                logger.info("API health monitoring started")
            except Exception as e:
                logger.error(f"Failed to start API health monitoring: {e}")

            # Start checkout queue processing and other background services
            try:
                from services.startup import initialize_services

                await initialize_services()
                logger.info("Background services initialized")
            except Exception as e:
                logger.error(f"Failed to start background services: {e}")

            logger.info("Bot is starting polling...")

            # Start polling with graceful shutdown
            # Use resolved update types and ensure callback queries are included
            resolved = set(self.dp.resolve_used_update_types() or [])
            # Always allow messages and callback queries
            resolved.update({"message", "callback_query"})
            polling_task = asyncio.create_task(
                self.dp.start_polling(self.bot, allowed_updates=list(resolved))
            )

            # Wait for shutdown signal
            try:
                await self._shutdown_event.wait()
            except asyncio.CancelledError:
                # Graceful cancellation (e.g., Ctrl+C)
                self._shutdown_event.set()
                raise

            # Cancel polling
            polling_task.cancel()
            try:
                await polling_task
            except asyncio.CancelledError:
                pass

            logger.info("Bot polling stopped")

        except asyncio.CancelledError:
            # Normal shutdown path due to cancellation
            pass
        except Exception as e:
            logger.error(f"Error during bot execution: {e}")
            raise

    async def _retention_worker(self) -> None:
        """Background worker for data retention cleanup"""
        try:
            from services.retention_service import RetentionService

            retention_service = RetentionService()
            logger.info("Retention worker started")
        except ImportError as e:
            logger.warning(f"Retention service not available: {e}")
            return
        except Exception as e:
            logger.error(f"Failed to initialize retention service: {e}")
            return

        while not self._shutdown_event.is_set():
            try:
                removed = await retention_service.purge_old_data(
                    self.settings.RETENTION_DAYS
                )
                if removed > 0:
                    logger.info(f"Retention cleanup removed {removed} records")
                else:
                    logger.debug("No old data found for retention cleanup")
            except Exception as e:
                logger.error(f"Retention cleanup failed: {e}")

            # Wait 24 hours or until shutdown
            try:
                await asyncio.wait_for(self._shutdown_event.wait(), timeout=24 * 3600)
                break  # Shutdown requested
            except asyncio.TimeoutError:
                continue  # Continue with next cleanup cycle

        logger.info("Retention worker stopped")

    async def shutdown(self) -> None:
        """Shutdown bot application"""
        try:
            logger.info("Shutting down bot application...")

            # Stop background tasks
            try:
                from services.background_tasks import stop_background_tasks

                await stop_background_tasks()
                logger.info("Background tasks stopped")
            except Exception as e:
                logger.error(f"Error stopping background tasks: {e}")

            # Stop checkout queue and other background services
            try:
                from services.startup import shutdown_services

                await shutdown_services()
                logger.info("Background services shutdown")
            except Exception as e:
                logger.error(f"Error stopping background services: {e}")

            # Close service sessions
            try:
                from services.card_service import CardService
                from services.cart_service import CartService

                card_service = CardService()
                await card_service.close()
                logger.info("Card service session closed")

                cart_service = CartService()
                await cart_service.close()
                logger.info("Cart service session closed")
            except Exception as e:
                logger.warning(f"Error closing services: {e}")

            if self.bot:
                await self.bot.session.close()

            await close_database()

            logger.info("Bot application shutdown completed")

            # Flush and close all logging handlers to avoid atexit errors
            logging.shutdown()

        except Exception as e:
            logger.error(f"Error during shutdown: {e}")


@asynccontextmanager
async def bot_lifespan() -> AsyncGenerator[BotApplication, None]:
    """Context manager for bot application lifecycle"""
    app = BotApplication()
    try:
        await app.setup()
        yield app
    finally:
        await app.shutdown()


async def main() -> None:
    """Main application entry point"""
    try:
        async with bot_lifespan() as app:
            await app.run()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except asyncio.CancelledError:
        logger.info("Shutdown requested (cancelled)")
    except Exception as e:
        logger.error(f"Application failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        pass
