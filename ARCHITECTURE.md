# Demo Wallet Bot v2 - Architecture Documentation

## Project Structure

```
bot_v2/
├── main.py                 # Application entry point
├── run.py                  # Standalone runner script
├── run.sh                  # Shell script for running the bot
├── setup.sh                # Automated setup script
├── requirements.txt        # Python dependencies
├── config.example.env      # Environment configuration template
├── README.md              # Project documentation
├── ARCHITECTURE.md        # This file
│
├── config/                # Configuration management
│   ├── __init__.py
│   └── settings.py        # Settings and environment handling
│
├── database/              # Database layer
│   ├── __init__.py
│   └── connection.py      # MongoDB connection and fallback
│
├── models/                # Data models
│   ├── __init__.py
│   ├── base.py           # Base model classes
│   ├── user.py           # User and wallet models
│   ├── transaction.py    # Transaction and purchase models
│   ├── catalog.py        # Catalog and cart models
│   └── api.py            # API configuration models
│
├── handlers/              # Telegram bot handlers
│   ├── __init__.py       # Handler setup and routing
│   ├── user_handlers.py  # User-facing handlers
│   ├── admin_handlers.py # Admin panel handlers (includes API Configuration UI)
│   ├── catalog_handlers.py   # Catalog browsing handlers
│   ├── cart_handlers.py      # Shopping cart handlers
│   ├── purchase_handlers.py  # Purchase flow handlers
│   ├── wallet_handlers.py    # Wallet management handlers
│   └── history_handlers.py   # Transaction history handlers
│
├── services/              # Business logic layer
│   ├── __init__.py
│   ├── user_service.py   # User management service
│   ├── card_service.py   # Card catalog service
│   ├── cart_service.py   # Shopping cart service
│   ├── api_service.py    # API configuration service
│   ├── export_service.py # Data export service
│   ├── notification_service.py # Notification service
│   ├── health_service.py      # Health monitoring service
│   ├── api_health_service.py  # API health monitoring
│   ├── api_health_monitor.py  # Health monitoring implementation
│   ├── background_tasks.py    # Background task management
│   ├── retention_service.py   # Data retention service
│   ├── api_analytics.py       # API analytics service
│   ├── api_import_export.py   # API import/export service
│   ├── api_security.py        # API security service
│   └── api_testing.py         # API testing service
│
├── middleware/            # Request middleware
│   ├── __init__.py
│   ├── admin_permissions.py # Admin permission checks
│   ├── error_handling.py    # Error handling middleware
│   ├── rate_limiting.py     # Rate limiting middleware
│   └── user_context.py      # User context middleware
│
├── utils/                 # Utility modules
│   ├── __init__.py
│   ├── keyboards.py      # Telegram keyboard definitions
│   ├── texts.py          # Text templates and messages
│   ├── logging.py        # Logging configuration
│   ├── security.py       # Security utilities
│   ├── validation.py     # Input validation utilities
│   ├── decorators.py     # Error handling decorators
│   ├── performance.py    # Performance monitoring
│   └── api_ui_helpers.py # API UI helper functions
│
│
└── tests/                 # Test suite
    └── test_api_management.py # API management tests
```

## Architecture Overview

### 1. Layered Architecture

The application follows a clean layered architecture:

- **Presentation Layer**: Telegram bot handlers (`handlers/`)
- **Business Logic Layer**: Services (`services/`)
- **Data Access Layer**: Models and database connection (`models/`, `database/`)
- **Cross-cutting Concerns**: Middleware, utilities, and configuration

### 2. Handler Organization

Handlers are organized by feature area:

- **User Handlers**: Main menu, settings, profile management
- **Admin Handlers**: Administrative functions, user management
  (Legacy API admin handlers were removed to reduce duplication.)
- **Catalog Handlers**: Card browsing, filtering, search
- **Cart Handlers**: Shopping cart operations
- **Purchase Handlers**: Purchase flow and confirmation
- **Wallet Handlers**: Wallet operations and balance management
- **History Handlers**: Transaction history and reporting

### 3. Service Layer

Services encapsulate business logic and provide clean APIs:

- **User Service**: User registration, profile management, authentication
- **Card Service**: Card catalog management, search, filtering
- **Cart Service**: Shopping cart operations, checkout processing
- **API Service**: External API configuration and management
- **Health Service**: System health monitoring and reporting
- **Export Service**: Data export in multiple formats
- **Background Tasks**: Automated maintenance and monitoring

### 4. Data Models

Models are organized by domain:

- **Base Models**: Common functionality (timestamps, soft delete)
- **User Models**: User profiles, wallets, authentication
- **Transaction Models**: Purchases, payments, history
- **Catalog Models**: Cards, categories, filters, cart items
- **API Models**: API configurations, health status, metrics

### 5. Middleware Stack

Middleware provides cross-cutting functionality:

- **Admin Permissions**: Authorization checks for admin functions
- **Error Handling**: Centralized error processing and logging
- **Rate Limiting**: Request throttling and abuse prevention
- **User Context**: User session and context management

### 6. Configuration Management

Configuration is handled through:

- **Environment Variables**: Sensitive configuration (tokens, keys)
- **Settings Module**: Application configuration with validation
- **Default Values**: Sensible defaults for all settings

## Key Design Patterns

### 1. Repository Pattern
Services act as repositories, abstracting data access and providing clean APIs.

### 2. Decorator Pattern
Decorators provide cross-cutting concerns like error handling, validation, and logging.

### 3. Factory Pattern
Router factories create and configure handler routers with appropriate middleware.

### 4. Observer Pattern
Background tasks monitor system health and respond to changes.

### 5. Strategy Pattern
Different export formats and API authentication methods use strategy pattern.

## Security Considerations

### 1. Input Validation
- All user inputs are validated using dedicated validation utilities
- SQL injection and XSS prevention through input sanitization
- Callback data validation to prevent manipulation

### 2. Authentication & Authorization
- Multi-layer admin authentication with session management
- Role-based access control for different user types
- Admin session timeouts and security checks

### 3. Rate Limiting
- Per-user rate limiting to prevent abuse
- Different limits for different types of operations
- Graceful degradation under high load

### 4. Data Protection
- Sensitive data encryption at rest
- Secure configuration management
- Audit logging for all administrative actions

## Performance Optimizations

### 1. Database Optimization
- Connection pooling for MongoDB
- Efficient queries with proper indexing
- Fallback to in-memory storage for development

### 2. Async Processing
- Full async/await implementation
- Background task processing
- Non-blocking I/O operations

### 3. Caching
- In-memory caching for frequently accessed data
- Session caching for user contexts
- Query result caching where appropriate

### 4. Resource Management
- Proper cleanup of database connections
- Memory-efficient pagination
- Graceful shutdown handling

## Error Handling Strategy

### 1. Layered Error Handling
- Validation errors at input layer
- Business logic errors at service layer
- Infrastructure errors at data layer

### 2. User-Friendly Messages
- Technical errors translated to user-friendly messages
- Contextual error information
- Graceful degradation on failures

### 3. Comprehensive Logging
- Structured logging with different levels
- Error tracking and monitoring
- Performance metrics collection

### 4. Recovery Mechanisms
- Automatic retry for transient failures
- Circuit breaker pattern for external services
- Graceful fallback options

## Deployment Considerations

### 1. Environment Support
- Development, staging, and production configurations
- Environment-specific feature flags
- Scalable configuration management

### 2. Monitoring & Observability
- Health check endpoints
- Metrics collection and reporting
- Log aggregation and analysis

### 3. Maintenance
- Automated data retention and cleanup
- Background health monitoring
- Performance optimization tools

This architecture provides a solid foundation for a scalable, maintainable, and secure Telegram bot application.
