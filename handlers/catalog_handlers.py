"""`r`nCatalog and browsing handlers`r`n"""

from __future__ import annotations

import logging

from aiogram import Router, F
from aiogram.types import CallbackQuery, InlineKeyboardMarkup, InlineKeyboardButton

from services.card_service import CardService
from services.cart_service import CartService
from services.user_service import UserService
from utils.keyboards import browse_menu_keyboard, filter_menu_keyboard, back_keyboard
from utils.texts import DEMO_WATERMARK, INFO_NO_RESULTS
from middleware import attach_common_middlewares

logger = logging.getLogger(__name__)


class CatalogHandlers:
    """Catalog and browsing handlers"""

    def __init__(self):
        self.card_service = CardService()
        # Lazy init to avoid DB access on import
        self.cart_service = None
        # Lazy to avoid DB access during simple handler init
        self.user_service = None
        # In-memory filter state per user
        self.user_filters: dict[int, dict] = {}
        self.user_applied_filters: dict[int, dict] = {}

        # Cache for current page cards to avoid re-fetching when adding to cart
        self.user_current_cards: dict[int, list[dict]] = {}

    def _get_user_id(self, callback: CallbackQuery) -> int:
        return callback.from_user.id if callback.from_user else 0

    def _get_temp_filters(self, user_id: int) -> dict:
        return self.user_filters.setdefault(user_id, {})

    def _get_applied_filters(self, user_id: int) -> dict:
        return self.user_applied_filters.get(user_id, {})

    def _filters_summary_text(self, filters: dict) -> str:
        """Generate a human-readable summary of active filters"""
        if not filters:
            return "No active filters"
        parts = []
        mapping = {
            "country": "Country",
            "bank": "Bank",
            "type": "Card Type",
            "brand": "Brand",
            "priceFrom": "Price From",
            "priceTo": "Price To",
            "base": "Base",
            "bin": "BIN",
            "state": "State",
            "city": "City",
            "zip": "ZIP",
        }

        # Handle price range specially
        if "priceFrom" in filters and "priceTo" in filters:
            price_from = filters["priceFrom"]
            price_to = filters["priceTo"]
            parts.append(f"• Price Range: ${price_from} - ${price_to}")
        elif "priceFrom" in filters:
            parts.append(f"• Price From: ${filters['priceFrom']}")
        elif "priceTo" in filters:
            parts.append(f"• Price To: ${filters['priceTo']}")

        # Handle other filters
        for k, v in filters.items():
            if k not in ["priceFrom", "priceTo"] and v:
                label = mapping.get(k, k.title())
                parts.append(f"• {label}: {v}")

        return "\n".join(parts)

    def _clear_user_filters(self, user_id: int) -> None:
        """Clear all filters for a user"""
        self.user_filters[user_id] = {}
        self.user_applied_filters[user_id] = {}

    def _has_active_filters(self, user_id: int) -> bool:
        """Check if user has any active filters"""
        applied = self._get_applied_filters(user_id)
        return bool(applied)

    def _build_filter_selection_keyboard(self, filter_key: str) -> InlineKeyboardMarkup:
        rows = []
        if filter_key == "country":
            options = ["US", "CA", "GB", "AU", "DE", "FR"]
            for i in range(0, len(options), 3):
                rows.append(
                    [
                        InlineKeyboardButton(
                            text=code, callback_data=f"filter:set:country:{code}"
                        )
                        for code in options[i : i + 3]
                    ]
                )
        elif filter_key == "brand":
            options = ["VISA", "MASTERCARD", "AMEX", "DISCOVER"]
            for i in range(0, len(options), 2):
                rows.append(
                    [
                        InlineKeyboardButton(
                            text=b, callback_data=f"filter:set:brand:{b}"
                        )
                        for b in options[i : i + 2]
                    ]
                )
        elif filter_key == "type":
            options = ["CREDIT", "DEBIT", "PREPAID"]
            rows.append(
                [
                    InlineKeyboardButton(text=o, callback_data=f"filter:set:type:{o}")
                    for o in options
                ]
            )
        elif filter_key == "price":
            ranges = [("$0 - $2", 0, 2), ("$2 - $3", 2, 3), ("$3 - $5", 3, 5)]
            for label, p_from, p_to in ranges:
                rows.append(
                    [
                        InlineKeyboardButton(
                            text=label,
                            callback_data=f"filter:set:price:{p_from}-{p_to}",
                        )
                    ]
                )
        elif filter_key == "bank":
            banks = [
                "CHASE",
                "CITI",
                "BANK OF AMERICA",
                "WELLS FARGO",
                "AMERICAN EXPRESS",
            ]
            for b in banks:
                rows.append(
                    [InlineKeyboardButton(text=b, callback_data=f"filter:set:bank:{b}")]
                )

        rows.append(
            [
                InlineKeyboardButton(text="◀️ Back", callback_data="catalog:filters"),
                InlineKeyboardButton(text="✅ Apply", callback_data="filter:apply"),
            ]
        )
        rows.append(
            [InlineKeyboardButton(text="🗑️ Clear", callback_data="filter:clear")]
        )
        return InlineKeyboardMarkup(inline_keyboard=rows)

    async def _render_cards_page(self, callback: CallbackQuery, page: int) -> None:
        """Render cards list for a given page using applied filters."""
        try:
            await callback.answer("⏳ Loading cards...")

            user_id = self._get_user_id(callback)
            applied = self._get_applied_filters(user_id)
            cards_data = await self.card_service.fetch_cards(
                page=page, limit=5, filters=applied
            )

            if not cards_data.get("success", False):
                error_msg = cards_data.get("error", "Unknown error")
                await callback.message.edit_text(
                    f"❌ <b>Error fetching cards</b>\n\n"
                    f"Error: {error_msg}" + DEMO_WATERMARK,
                    reply_markup=back_keyboard("menu:browse"),
                )
                return

            cards = cards_data.get("data", [])
            total_count = cards_data.get("totalCount", 0)

            # Cache the current cards for this user
            self.user_current_cards[user_id] = cards

            if not cards:
                await callback.message.edit_text(
                    "📭 <b>No cards found</b>\n\nNo cards available on this page."
                    + DEMO_WATERMARK,
                    reply_markup=back_keyboard("menu:browse"),
                )
                return

            # Format cards for display with filter status
            user_id = self._get_user_id(callback)
            applied_filters = self._get_applied_filters(user_id)

            cards_text = f"🛒 <b>Cards (Page {page})</b>\n\n"

            # Show active filters if any
            if applied_filters:
                filter_summary = self._filters_summary_text(applied_filters)
                cards_text += f"🔍 <b>Active Filters:</b>\n{filter_summary}\n\n"
                cards_text += "─" * 40 + "\n\n"

            for i, card in enumerate(cards, 1):
                cards_text += (
                    f"<b>{i}.</b> "
                    + self.card_service.format_card_for_display(card)
                    + "\n"
                )
                if i < len(cards):
                    cards_text += "—" * 30 + "\n\n"

            # Create pagination keyboard
            keyboard_buttons = []

            nav_buttons = []
            if page > 1:
                nav_buttons.append(
                    InlineKeyboardButton(
                        text="⬅️ Previous",
                        callback_data=f"catalog:view_cards:{page-1}",
                    )
                )

            max_page = (total_count + 4) // 5
            if page < max_page:
                nav_buttons.append(
                    InlineKeyboardButton(
                        text="➡️ Next", callback_data=f"catalog:view_cards:{page+1}"
                    )
                )

            if nav_buttons:
                keyboard_buttons.append(nav_buttons)

            if cards:
                cart_buttons = []
                for card in cards[:5]:
                    card_id = card.get("_id")
                    bin_number = card.get("bin", "N/A")
                    cart_buttons.append(
                        InlineKeyboardButton(
                            text=f"🛒 Add {bin_number}",
                            callback_data=f"catalog:add_to_cart:{card_id}",
                        )
                    )

                for i in range(0, len(cart_buttons), 2):
                    keyboard_buttons.append(cart_buttons[i : i + 2])

            # Action buttons with filter-aware options
            if applied_filters:
                keyboard_buttons.append(
                    [
                        InlineKeyboardButton(
                            text="🔍 New Search", callback_data="catalog:search"
                        ),
                        InlineKeyboardButton(
                            text="🧰 Edit Filters", callback_data="catalog:filters"
                        ),
                    ]
                )
                keyboard_buttons.append(
                    [
                        InlineKeyboardButton(
                            text="🧹 Clear Filters", callback_data="filter:clear"
                        ),
                        InlineKeyboardButton(
                            text="🛒 View Cart", callback_data="local:cart:view"
                        ),
                    ]
                )
            else:
                keyboard_buttons.append(
                    [
                        InlineKeyboardButton(
                            text="🔍 New Search", callback_data="catalog:search"
                        ),
                        InlineKeyboardButton(
                            text="🛒 View Cart", callback_data="local:cart:view"
                        ),
                    ]
                )

            keyboard_buttons.append(
                [InlineKeyboardButton(text="◀️ Back", callback_data="menu:browse")]
            )

            keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

            footer_text = (
                f"\n\n📊 <b>Page {page} of {max_page}</b> | "
                f"Total: {total_count:,} cards\n"
                "<i>This is demo data from an external API</i>" + DEMO_WATERMARK
            )

            await callback.message.edit_text(
                cards_text + footer_text,
                reply_markup=keyboard,
            )
        except Exception as e:
            logger.error(f"Error rendering cards page: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_browse_menu(self, callback: CallbackQuery) -> None:
        """Handle browse menu callback"""
        try:
            user_id = self._get_user_id(callback)
            applied_filters = self._get_applied_filters(user_id)

            # Build message with filter status
            message = "🛒 <b>Browse Catalog</b>\n\n"

            if applied_filters:
                filter_summary = self._filters_summary_text(applied_filters)
                message += f"🔍 <b>Active Filters:</b>\n{filter_summary}\n\n"
                message += "Choose an option to explore available items:"
            else:
                message += "Choose an option to explore available items:"

            # Create enhanced browse menu with filter-aware options
            keyboard_buttons = [
                [
                    InlineKeyboardButton(
                        text="🔎 Search", callback_data="catalog:search"
                    ),
                    InlineKeyboardButton(
                        text="🧰 Filters", callback_data="catalog:filters"
                    ),
                ],
            ]

            # Add browse all option
            if applied_filters:
                keyboard_buttons.append(
                    [
                        InlineKeyboardButton(
                            text="📄 Browse with Filters",
                            callback_data="catalog:browse_filtered",
                        ),
                        InlineKeyboardButton(
                            text="📋 Browse All", callback_data="catalog:browse_all"
                        ),
                    ]
                )
            else:
                keyboard_buttons.append(
                    [
                        InlineKeyboardButton(
                            text="📄 Browse All", callback_data="catalog:browse_all"
                        ),
                    ]
                )

            keyboard_buttons.extend(
                [
                    [
                        InlineKeyboardButton(
                            text="⚡ Quick Buy", callback_data="catalog:quick_buy"
                        ),
                        InlineKeyboardButton(
                            text="⭐ Favorites", callback_data="catalog:favorites"
                        ),
                    ],
                    [InlineKeyboardButton(text="⬅️ Back", callback_data="menu:main")],
                ]
            )

            keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

            await callback.message.edit_text(
                message + DEMO_WATERMARK,
                reply_markup=keyboard,
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in browse menu: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_search(self, callback: CallbackQuery) -> None:
        """Handle search callback"""
        try:
            await callback.answer("🔍 Fetching cards...")

            # Fetch cards from the API
            cards_data = await self.card_service.fetch_cards(
                page=1,
                limit=5,
                filters=self._get_applied_filters(self._get_user_id(callback)),
            )

            if not cards_data.get("success", False):
                error_msg = cards_data.get("error", "Unknown error")
                await callback.message.edit_text(
                    f"❌ <b>Error fetching cards</b>\n\n"
                    f"Error: {error_msg}\n\n"
                    "<i>This is demo data from an external API</i>" + DEMO_WATERMARK,
                    reply_markup=back_keyboard("menu:browse"),
                )
                return

            cards = cards_data.get("data", [])
            if not cards:
                await callback.message.edit_text(
                    INFO_NO_RESULTS + "\n\n"
                    "<i>No cards found with current filters</i>" + DEMO_WATERMARK,
                    reply_markup=back_keyboard("menu:browse"),
                )
                return

            # Generate summary and show first few cards
            summary = await self.card_service.get_card_summary(cards_data)

            # Create pagination keyboard
            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="📄 View Cards", callback_data="catalog:view_cards:1"
                        ),
                        InlineKeyboardButton(
                            text="🔍 New Search", callback_data="catalog:search"
                        ),
                    ],
                    [InlineKeyboardButton(text="🔙 Back", callback_data="menu:browse")],
                ]
            )

            await callback.message.edit_text(
                summary
                + "\n\n<i>This is demo data from an external API</i>"
                + DEMO_WATERMARK,
                reply_markup=keyboard,
            )

        except Exception as e:
            logger.error(f"Error in search: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_filters(self, callback: CallbackQuery) -> None:
        """Handle filters callback"""
        try:
            user_id = self._get_user_id(callback)
            temp_filters = self._get_temp_filters(user_id)
            applied_filters = self._get_applied_filters(user_id)

            # Build message with current filter status
            message = "🗂️ <b>Search Filters</b>\n\n"

            if temp_filters:
                temp_summary = self._filters_summary_text(temp_filters)
                message += f"⚙️ <b>Pending Filters:</b>\n{temp_summary}\n\n"

            if applied_filters:
                applied_summary = self._filters_summary_text(applied_filters)
                message += f"✅ <b>Applied Filters:</b>\n{applied_summary}\n\n"

            message += "Configure your search criteria:"

            await callback.message.edit_text(
                message + DEMO_WATERMARK,
                reply_markup=filter_menu_keyboard(),
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in filters: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_quick_buy(self, callback: CallbackQuery) -> None:
        """Handle quick buy callback"""
        try:
            await callback.message.edit_text(
                "💳 <b>Quick Buy</b>\n\n"
                "No items available for quick purchase in this demo." + DEMO_WATERMARK,
                reply_markup=back_keyboard("menu:browse"),
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in quick buy: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_favorites(self, callback: CallbackQuery) -> None:
        """Handle favorites callback"""
        try:
            await callback.message.edit_text(
                "⭐ <b>Favorites</b>\n\n"
                "You haven't saved any favorites yet." + DEMO_WATERMARK,
                reply_markup=back_keyboard("menu:browse"),
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in favorites: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_browse_filtered(self, callback: CallbackQuery) -> None:
        """Handle browse with filters callback"""
        try:
            user_id = self._get_user_id(callback)
            applied_filters = self._get_applied_filters(user_id)

            if not applied_filters:
                await callback.answer(
                    "No filters applied. Use 'Browse All' instead.", show_alert=True
                )
                return

            await callback.answer("🔍 Loading filtered results...")
            await self._render_cards_page(callback, page=1)

        except Exception as e:
            logger.error(f"Error in browse filtered: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_browse_all(self, callback: CallbackQuery) -> None:
        """Handle browse all callback (no filters)"""
        try:
            user_id = self._get_user_id(callback)
            # Temporarily clear applied filters for this browse session
            original_filters = self._get_applied_filters(user_id).copy()
            self.user_applied_filters[user_id] = {}

            await callback.answer("📄 Loading all cards...")
            await self._render_cards_page(callback, page=1)

            # Restore original filters after rendering
            self.user_applied_filters[user_id] = original_filters

        except Exception as e:
            logger.error(f"Error in browse all: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_filter_actions(self, callback: CallbackQuery) -> None:
        """Handle filter action callbacks"""
        try:
            parts = callback.data.split(":")
            action = parts[1] if len(parts) > 1 else ""

            if action == "clear":
                user_id = self._get_user_id(callback)
                self._clear_user_filters(user_id)
                await callback.message.edit_text(
                    "??? <b>Filters Cleared</b>\n\nAll search filters have been reset."
                    + DEMO_WATERMARK,
                    reply_markup=filter_menu_keyboard(),
                )
                await callback.answer("? Filters cleared")

            elif action == "apply":
                user_id = self._get_user_id(callback)
                self.user_applied_filters[user_id] = self._get_temp_filters(
                    user_id
                ).copy()
                await callback.answer("? Filters applied")
                # Jump to first page of results with applied filters
                await self._render_cards_page(callback, page=1)

            elif action == "set" and len(parts) >= 4:
                user_id = self._get_user_id(callback)
                key = parts[2]
                value = parts[3]
                temp = self._get_temp_filters(user_id)

                # Validate and set filter values
                if key == "price" and "-" in value:
                    try:
                        p_from, p_to = value.split("-", 1)
                        # Comprehensive price range validation
                        price_from = float(p_from.strip())
                        price_to = float(p_to.strip())

                        # Validate price bounds
                        if price_from < 0 or price_to < 0:
                            await callback.answer(
                                "❌ Prices cannot be negative", show_alert=True
                            )
                            return
                        if price_from > price_to:
                            await callback.answer(
                                "❌ Minimum price cannot exceed maximum price",
                                show_alert=True,
                            )
                            return
                        if price_from > 10000 or price_to > 10000:
                            await callback.answer(
                                "❌ Price range too high (max $10,000)", show_alert=True
                            )
                            return
                        if price_to - price_from > 5000:
                            await callback.answer(
                                "❌ Price range too wide (max $5,000 difference)",
                                show_alert=True,
                            )
                            return

                        # Format prices to 2 decimal places
                        price_from = round(price_from, 2)
                        price_to = round(price_to, 2)

                        temp["priceFrom"] = str(price_from)
                        temp["priceTo"] = str(price_to)
                        await callback.answer(
                            f"✅ Price range set: ${price_from:.2f} - ${price_to:.2f}"
                        )
                    except ValueError:
                        await callback.answer(
                            "❌ Invalid price format. Use numbers only (e.g., 10.50-100.00)",
                            show_alert=True,
                        )
                        return
                else:
                    # Validate other filter values with enhanced security
                    from utils.validation import sanitize_text_input, ValidationError

                    if not value or not value.strip():
                        await callback.answer("❌ Empty filter value", show_alert=True)
                        return

                    try:
                        # Sanitize and validate the input
                        sanitized_value = sanitize_text_input(
                            value.strip(), max_length=50
                        )

                        # Additional validation based on filter type
                        if key in ["country", "brand", "type"]:
                            # Allow only alphanumeric characters, spaces, and common symbols
                            import re

                            if not re.match(r"^[a-zA-Z0-9\s\-_\.]+$", sanitized_value):
                                await callback.answer(
                                    "❌ Invalid characters in filter value",
                                    show_alert=True,
                                )
                                return

                        temp[key] = sanitized_value
                        await callback.answer(
                            f"✅ {key.title()} set to: {sanitized_value}"
                        )

                    except ValidationError as e:
                        await callback.answer(f"❌ {str(e)}", show_alert=True)
                        return

                # Return to filter menu with updated display
                await self.cb_filters(callback)

            else:
                # Open specific selection UIs
                if action in {"country", "brand", "price", "bank", "type"}:
                    kb = self._build_filter_selection_keyboard(action)
                    await callback.message.edit_text(
                        f"?? <b>{action.title()} Filter</b>\n\nChoose an option:"
                        + DEMO_WATERMARK,
                        reply_markup=kb,
                    )
                    await callback.answer()
                else:
                    await callback.answer("Unknown filter action", show_alert=True)

        except Exception as e:
            logger.error(f"Error in filter actions: {e}")
            await callback.answer("? Error occurred", show_alert=True)

    async def cb_view_cards(self, callback: CallbackQuery) -> None:
        """Handle view cards callback with pagination"""
        try:
            # Extract page number from callback data
            parts = callback.data.split(":")
            page = int(parts[2]) if len(parts) > 2 else 1

            await callback.answer("📄 Loading cards...")

            # Fetch cards for the specified page
            user_id = self._get_user_id(callback)
            applied = self._get_applied_filters(user_id)
            cards_data = await self.card_service.fetch_cards(
                page=page, limit=5, filters=applied
            )

            if not cards_data.get("success", False):
                error_msg = cards_data.get("error", "Unknown error")
                await callback.message.edit_text(
                    f"❌ <b>Error fetching cards</b>\n\n"
                    f"Error: {error_msg}" + DEMO_WATERMARK,
                    reply_markup=back_keyboard("menu:browse"),
                )
                return

            cards = cards_data.get("data", [])
            total_count = cards_data.get("totalCount", 0)

            # Cache the current cards for this user
            self.user_current_cards[user_id] = cards

            if not cards:
                await callback.message.edit_text(
                    "📭 <b>No cards found</b>\n\n"
                    "No cards available on this page." + DEMO_WATERMARK,
                    reply_markup=back_keyboard("menu:browse"),
                )
                return

            # Format cards for display
            cards_text = f"💳 <b>Cards (Page {page})</b>\n\n"

            for i, card in enumerate(cards, 1):
                cards_text += (
                    f"<b>{i}.</b> "
                    + self.card_service.format_card_for_display(card)
                    + "\n"
                )
                if i < len(cards):  # Add separator between cards
                    cards_text += "─" * 30 + "\n\n"

            # Create pagination keyboard
            keyboard_buttons = []

            # Navigation buttons
            nav_buttons = []
            if page > 1:
                nav_buttons.append(
                    InlineKeyboardButton(
                        text="⬅️ Previous", callback_data=f"catalog:view_cards:{page-1}"
                    )
                )

            # Calculate if there are more pages (assuming 5 cards per page)
            max_page = (total_count + 4) // 5  # Round up division
            if page < max_page:
                nav_buttons.append(
                    InlineKeyboardButton(
                        text="➡️ Next", callback_data=f"catalog:view_cards:{page+1}"
                    )
                )

            if nav_buttons:
                keyboard_buttons.append(nav_buttons)

            # Add to cart buttons for each card
            if cards:
                cart_buttons = []
                for card in cards[:5]:  # Show add to cart for first 5 cards
                    card_id = card.get("_id")
                    bin_number = card.get("bin", "N/A")
                    cart_buttons.append(
                        InlineKeyboardButton(
                            text=f"🛒 Add {bin_number}",
                            callback_data=f"catalog:add_to_cart:{card_id}",
                        )
                    )

                # Split into rows of 2 buttons each
                for i in range(0, len(cart_buttons), 2):
                    keyboard_buttons.append(cart_buttons[i : i + 2])

            # Action buttons
            keyboard_buttons.append(
                [
                    InlineKeyboardButton(
                        text="🔍 New Search", callback_data="catalog:search"
                    ),
                    InlineKeyboardButton(
                        text="🛒 View Cart", callback_data="local:cart:view"
                    ),
                ]
            )
            keyboard_buttons.append(
                [InlineKeyboardButton(text="🔙 Back", callback_data="menu:browse")]
            )

            keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

            footer_text = (
                f"\n\n📊 <b>Page {page} of {max_page}</b> | "
                f"Total: {total_count:,} cards\n"
                "<i>This is demo data from an external API</i>" + DEMO_WATERMARK
            )

            await callback.message.edit_text(
                cards_text + footer_text,
                reply_markup=keyboard,
            )

        except Exception as e:
            logger.error(f"Error in view cards: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_add_to_cart(self, callback: CallbackQuery) -> None:
        """Handle add to cart callback"""
        try:
            if self.user_service is None:
                self.user_service = UserService()
            if self.cart_service is None:
                self.cart_service = CartService()
            # Extract card ID from callback data
            parts = callback.data.split(":")
            if len(parts) < 3:
                await callback.answer("❌ Invalid request", show_alert=True)
                return

            card_id = int(parts[2])

            user = callback.from_user
            if not user:
                await callback.answer("❌ Unable to identify user", show_alert=True)
                return

            # Get user document
            user_doc = await self.user_service.get_user_by_telegram_id(user.id)
            if not user_doc:
                await callback.answer(
                    "❌ User not found. Please start the bot with /start",
                    show_alert=True,
                )
                return

            await callback.answer("🛒 Adding to cart...")

            # Find card data from cache
            user_id = self._get_user_id(callback)
            cached_cards = self.user_current_cards.get(user_id, [])
            card_data = None

            for card in cached_cards:
                if card.get("_id") == card_id:
                    card_data = card
                    break

            # Add to cart with card data if available
            success, message = await self.cart_service.add_to_cart(
                str(user_doc.id), card_id, 1, card_data
            )

            if success:
                # Get updated cart count
                cart_count = await self.cart_service.get_cart_item_count(
                    str(user_doc.id)
                )

                await callback.answer(f"✅ {message} (Cart: {cart_count} items)")

                # Update the message to show cart option
                current_text = callback.message.text or callback.message.caption or ""

                # Add cart notification to current message
                if "🛒 Item added to cart!" not in current_text:
                    updated_text = (
                        current_text
                        + f"\n\n🛒 <b>Item added to cart!</b> (Total: {cart_count} items)"
                    )

                    # Create updated keyboard with cart view option
                    keyboard = InlineKeyboardMarkup(
                        inline_keyboard=[
                            [
                                InlineKeyboardButton(
                                    text="🛒 View Cart", callback_data="local:cart:view"
                                ),
                                InlineKeyboardButton(
                                    text="🔍 Continue Shopping",
                                    callback_data="catalog:search",
                                ),
                            ],
                            [
                                InlineKeyboardButton(
                                    text="🔙 Back", callback_data="menu:browse"
                                )
                            ],
                        ]
                    )

                    await callback.message.edit_text(
                        updated_text,
                        reply_markup=keyboard,
                    )
            else:
                await callback.answer(f"❌ {message}", show_alert=True)

        except Exception as e:
            logger.error(f"Error adding to cart: {e}")
            await callback.answer("❌ Error adding to cart", show_alert=True)


def get_catalog_router() -> Router:
    """Create and return catalog router"""
    router = Router()
    attach_common_middlewares(router)
    handlers = CatalogHandlers()

    # Callback handlers
    router.callback_query.register(handlers.cb_browse_menu, F.data == "menu:browse")
    router.callback_query.register(handlers.cb_search, F.data == "catalog:search")
    router.callback_query.register(handlers.cb_filters, F.data == "catalog:filters")
    router.callback_query.register(handlers.cb_quick_buy, F.data == "catalog:quick_buy")
    router.callback_query.register(handlers.cb_favorites, F.data == "catalog:favorites")

    # Browse handlers
    router.callback_query.register(
        handlers.cb_browse_filtered, F.data == "catalog:browse_filtered"
    )
    router.callback_query.register(
        handlers.cb_browse_all, F.data == "catalog:browse_all"
    )

    router.callback_query.register(
        handlers.cb_view_cards, F.data.startswith("catalog:view_cards:")
    )
    router.callback_query.register(
        handlers.cb_add_to_cart, F.data.startswith("catalog:add_to_cart:")
    )

    # Filter callbacks
    router.callback_query.register(
        handlers.cb_filter_actions, F.data.startswith("filter:")
    )

    logger.info("Catalog handlers registered")
    return router
