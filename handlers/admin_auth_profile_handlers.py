"""
Admin panel handlers for Authentication Profile management
Provides UI for creating, managing, and assigning authentication profiles
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
import json

from aiogram import Router, F
from aiogram.types import (
    Message,
    CallbackQuery,
    InlineKeyboardMarkup,
    InlineKeyboardButton,
)
import html as _html
from aiogram.filters import Command
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup

from services.auth_profile_service import get_auth_profile_service
from services.api_config_service import get_api_config_service
from models.auth_profile import (
    AuthProfileScope,
    AuthProfileStatus,
    AuthProfileCredentials,
)
from models.api import AuthenticationType, APIEnvironment
from services.user_service import get_user_service
from utils.decorators import admin_required, error_handler
from utils.keyboards import create_pagination_keyboard
from middleware import attach_common_middlewares

logger = logging.getLogger(__name__)

# Create router
router = Router()
attach_common_middlewares(router)


class AuthProfileStates(StatesGroup):
    """States for authentication profile management"""

    # Profile creation
    WAITING_PROFILE_NAME = State()
    WAITING_DISPLAY_NAME = State()
    WAITING_DESCRIPTION = State()
    WAITING_AUTH_TYPE = State()
    WAITING_CREDENTIALS = State()

    # Credential input by type
    WAITING_API_KEY = State()
    WAITING_BEARER_TOKEN = State()
    WAITING_USERNAME = State()
    WAITING_PASSWORD = State()
    WAITING_OAUTH_CLIENT_ID = State()
    WAITING_OAUTH_CLIENT_SECRET = State()
    WAITING_OAUTH_TOKEN_URL = State()

    # Profile assignment
    WAITING_PROFILE_SELECTION = State()
    WAITING_OVERRIDE_CREDENTIALS = State()

    # Bulk operations
    WAITING_BULK_UPDATE_DATA = State()


@router.message(Command("auth_profiles"))
@admin_required
@error_handler
async def cmd_auth_profiles(message: Message, state: FSMContext):
    """Main authentication profiles management command"""
    await _show_profiles_main_menu(message, state, is_callback=False)


async def _show_profiles_main_menu(
    message_or_callback,
    state: FSMContext,
    is_callback: bool = True,
    scope_filter: Optional[AuthProfileScope] = None,
):
    """Show main authentication profiles menu"""
    auth_service = get_auth_profile_service()

    # Get profiles with optional filtering
    profiles = await auth_service.list_profiles(scope=scope_filter)

    # Build header
    header = "🔐 <b>Authentication Profiles Management</b>"
    if scope_filter:
        header += f" - Scope: {scope_filter.value.title()}"
    header += "\n\n"

    if not profiles:
        no_profiles_text = header + "No authentication profiles found."
        if scope_filter:
            no_profiles_text += "\n\n🔍 Try adjusting your filters."
        else:
            no_profiles_text += (
                "\n\nGet started by creating your first authentication profile!"
            )

        keyboard = [
            [
                InlineKeyboardButton(
                    text="➕ Create Profile", callback_data="auth_profile_create"
                )
            ],
            [
                InlineKeyboardButton(
                    text="📋 Create from Template",
                    callback_data="auth_profile_template",
                )
            ],
            [
                InlineKeyboardButton(
                    text="📥 Import Profiles", callback_data="auth_profile_import"
                )
            ],
        ]

        if scope_filter:
            keyboard.append(
                [
                    InlineKeyboardButton(
                        text="🔄 Clear Filters", callback_data="auth_profiles_main"
                    )
                ]
            )

        reply_markup = InlineKeyboardMarkup(inline_keyboard=keyboard)

        if is_callback:
            await message_or_callback.message.edit_text(
                no_profiles_text, parse_mode="HTML", reply_markup=reply_markup
            )
        else:
            await message_or_callback.answer(
                no_profiles_text, parse_mode="HTML", reply_markup=reply_markup
            )
        return

    # Group profiles by scope
    scopes = {}
    for profile in profiles:
        scope = profile.scope
        if scope not in scopes:
            scopes[scope] = []
        scopes[scope].append(profile)

    # Build profiles list
    profiles_text = header
    keyboard = []

    # Quick stats
    total_profiles = len(profiles)
    active_profiles = sum(1 for p in profiles if p.is_active())
    profiles_text += f"📊 <b>Summary:</b> {active_profiles}/{total_profiles} active, {len(scopes)} scopes\n\n"

    # Show profiles by scope
    for scope, scope_profiles in sorted(scopes.items()):
        scope_icon = {
            AuthProfileScope.GLOBAL: "🌍",
            AuthProfileScope.CATEGORY: "📂",
            AuthProfileScope.PROVIDER: "🏢",
            AuthProfileScope.CUSTOM: "⚙️",
        }.get(scope, "📁")

        profiles_text += f"{scope_icon} <b>{scope.value.title()}</b>\n"

        for profile in sorted(scope_profiles, key=lambda p: p.display_name):
            status = "🟢" if profile.is_active() else "🔴"
            auth_type_icon = {
                AuthenticationType.API_KEY: "🔑",
                AuthenticationType.BEARER_TOKEN: "🎫",
                AuthenticationType.BASIC_AUTH: "🔐",
                AuthenticationType.OAUTH2: "🔒",
                AuthenticationType.CUSTOM_HEADER: "📋",
            }.get(profile.auth_type, "❓")

            profiles_text += (
                f"  {status} {auth_type_icon} {_html.escape(profile.display_name)}\n"
            )
            profiles_text += f"    📊 Used by {profile.usage_count} APIs\n"

            keyboard.append(
                [
                    InlineKeyboardButton(
                        text=f"⚙️ {profile.display_name}",
                        callback_data=f"auth_profile_view:{profile.id}",
                    )
                ]
            )

        profiles_text += "\n"

    # Action buttons
    action_buttons = [
        [
            InlineKeyboardButton(
                text="➕ Create Profile", callback_data="auth_profile_create"
            ),
            InlineKeyboardButton(
                text="📋 From Template", callback_data="auth_profile_template"
            ),
        ],
        [
            InlineKeyboardButton(text="📥 Import", callback_data="auth_profile_import"),
            InlineKeyboardButton(text="📤 Export", callback_data="auth_profile_export"),
        ],
        [
            InlineKeyboardButton(
                text="🔧 Bulk Update", callback_data="auth_profile_bulk"
            ),
            InlineKeyboardButton(
                text="📊 Usage Report", callback_data="auth_profile_usage"
            ),
        ],
        [
            InlineKeyboardButton(
                text="🔍 Filter by Scope", callback_data="auth_profile_filter"
            ),
            InlineKeyboardButton(text="🔄 Refresh", callback_data="auth_profiles_main"),
        ],
    ]

    if scope_filter:
        action_buttons.append(
            [
                InlineKeyboardButton(
                    text="🔄 Clear Filters", callback_data="auth_profiles_main"
                )
            ]
        )

    keyboard.extend(action_buttons)

    reply_markup = InlineKeyboardMarkup(inline_keyboard=keyboard)

    if is_callback:
        await message_or_callback.message.edit_text(
            profiles_text, parse_mode="HTML", reply_markup=reply_markup
        )
    else:
        await message_or_callback.answer(
            profiles_text, parse_mode="HTML", reply_markup=reply_markup
        )


@router.callback_query(F.data == "auth_profiles_main")
@admin_required
@error_handler
async def callback_auth_profiles_main(callback: CallbackQuery, state: FSMContext):
    """Show main authentication profiles menu"""
    await _show_profiles_main_menu(callback, state)


@router.callback_query(F.data.startswith("auth_profile_view:"))
@admin_required
@error_handler
async def callback_view_profile(callback: CallbackQuery, state: FSMContext):
    """View authentication profile details"""
    profile_id = callback.data.split(":", 1)[1]
    auth_service = get_auth_profile_service()

    profile = await auth_service.get_profile_by_id(profile_id)
    if not profile:
        await callback.answer("❌ Profile not found", show_alert=True)
        return

    # Get impact analysis
    impact = await auth_service.get_profile_impact_analysis(profile_id)

    text = f"🔐 <b>Authentication Profile: {_html.escape(profile.display_name)}</b>\n\n"

    # Basic information
    text += f"📝 <b>Profile Name:</b> <code>{profile.profile_name}</code>\n"
    text += f"🔧 <b>Auth Type:</b> {profile.auth_type.value.title()}\n"
    text += f"🌍 <b>Scope:</b> {profile.scope.value.title()}\n"
    text += f"🏷️ <b>Environment:</b> {profile.environment.value.title()}\n"

    status_icon = "🟢" if profile.is_active() else "🔴"
    text += f"📊 <b>Status:</b> {status_icon} {profile.status.value.title()}\n\n"

    # Description
    if profile.description:
        text += f"📄 <b>Description:</b>\n{_html.escape(profile.description)}\n\n"

    # Usage information
    text += f"📈 <b>Usage Statistics:</b>\n"
    text += f"• Used by {impact.get('total_assignments', 0)} API configurations\n"
    text += f"• Active APIs: {len(impact.get('active_apis', []))}\n"
    text += f"• Inactive APIs: {len(impact.get('inactive_apis', []))}\n"
    text += f"• APIs with overrides: {len(impact.get('apis_with_overrides', []))}\n\n"

    # Categories and environments affected
    if impact.get("categories_affected"):
        text += f"📂 <b>Categories:</b> {', '.join(impact['categories_affected'])}\n"
    if impact.get("environments_affected"):
        text += (
            f"🌍 <b>Environments:</b> {', '.join(impact['environments_affected'])}\n\n"
        )

    # Provider information
    if profile.provider_name:
        text += f"🏢 <b>Provider:</b> {profile.provider_name}\n"

    # Last updated
    if profile.last_used_at:
        text += f"🕒 <b>Last Used:</b> {profile.last_used_at.strftime('%Y-%m-%d %H:%M UTC')}\n"

    keyboard = [
        [
            InlineKeyboardButton(
                text="✏️ Edit Profile", callback_data=f"auth_profile_edit:{profile_id}"
            ),
            InlineKeyboardButton(
                text="🧪 Test Profile", callback_data=f"auth_profile_test:{profile_id}"
            ),
        ],
        [
            InlineKeyboardButton(
                text="📋 View APIs", callback_data=f"auth_profile_apis:{profile_id}"
            ),
            InlineKeyboardButton(
                text="📊 Usage Logs", callback_data=f"auth_profile_logs:{profile_id}"
            ),
        ],
        [
            InlineKeyboardButton(
                text="🔄 Duplicate",
                callback_data=f"auth_profile_duplicate:{profile_id}",
            ),
            InlineKeyboardButton(
                text="🗑️ Delete", callback_data=f"auth_profile_delete:{profile_id}"
            ),
        ],
        [InlineKeyboardButton(text="⬅️ Back", callback_data="auth_profiles_main")],
    ]

    await callback.message.edit_text(
        text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )


@router.callback_query(F.data == "auth_profile_create")
@admin_required
@error_handler
async def callback_create_profile(callback: CallbackQuery, state: FSMContext):
    """Start authentication profile creation"""
    await state.set_state(AuthProfileStates.WAITING_PROFILE_NAME)

    text = "➕ <b>Create Authentication Profile</b>\n\n"
    text += "Let's create a new authentication profile that can be shared across multiple API configurations.\n\n"
    text += "📝 <b>Step 1:</b> Enter a unique profile name\n\n"
    text += "💡 <b>Tips:</b>\n"
    text += "• Use lowercase letters, numbers, underscores, and hyphens\n"
    text += (
        "• Make it descriptive (e.g., 'shopify_store_auth', 'payment_gateway_prod')\n"
    )
    text += "• Keep it concise but meaningful\n\n"
    text += "Please enter the profile name:"

    keyboard = [
        [InlineKeyboardButton(text="❌ Cancel", callback_data="auth_profiles_main")]
    ]

    await callback.message.edit_text(
        text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )


@router.message(AuthProfileStates.WAITING_PROFILE_NAME)
@admin_required
@error_handler
async def message_profile_name(message: Message, state: FSMContext):
    """Handle profile name input"""
    profile_name = message.text.strip().lower()

    # Validate profile name
    import re

    if not re.match(r"^[a-z0-9_-]+$", profile_name):
        await message.answer(
            "❌ Invalid profile name format.\n\n"
            "Please use only lowercase letters, numbers, underscores, and hyphens."
        )
        return

    # Check if profile name already exists
    auth_service = get_auth_profile_service()
    existing = await auth_service.get_profile_by_name(profile_name)
    if existing:
        await message.answer(
            f"❌ Profile '{profile_name}' already exists.\n\n"
            "Please choose a different name."
        )
        return

    # Store profile name and move to next step
    await state.update_data(profile_name=profile_name)
    await state.set_state(AuthProfileStates.WAITING_DISPLAY_NAME)

    text = f"✅ Profile name: <code>{profile_name}</code>\n\n"
    text += "📝 <b>Step 2:</b> Enter a display name\n\n"
    text += "This is the human-readable name that will be shown in the admin panel.\n\n"
    text += (
        "Example: 'Shopify Store Authentication' or 'Production Payment Gateway'\n\n"
    )
    text += "Please enter the display name:"

    keyboard = [
        [InlineKeyboardButton(text="❌ Cancel", callback_data="auth_profiles_main")]
    ]

    await message.answer(
        text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )


@router.message(AuthProfileStates.WAITING_DISPLAY_NAME)
@admin_required
@error_handler
async def message_display_name(message: Message, state: FSMContext):
    """Handle display name input"""
    display_name = message.text.strip()

    if not display_name:
        await message.answer("❌ Display name cannot be empty.")
        return

    # Store display name and move to next step
    await state.update_data(display_name=display_name)
    await state.set_state(AuthProfileStates.WAITING_DESCRIPTION)

    data = await state.get_data()

    text = f"✅ Profile name: <code>{data['profile_name']}</code>\n"
    text += f"✅ Display name: {_html.escape(display_name)}\n\n"
    text += "📝 <b>Step 3:</b> Enter a description (optional)\n\n"
    text += (
        "Provide a brief description of what this authentication profile is for.\n\n"
    )
    text += "You can also type 'skip' to skip this step.\n\n"
    text += "Please enter the description:"

    keyboard = [
        [
            InlineKeyboardButton(
                text="⏭️ Skip", callback_data="auth_profile_skip_description"
            )
        ],
        [InlineKeyboardButton(text="❌ Cancel", callback_data="auth_profiles_main")],
    ]

    await message.answer(
        text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )


@router.callback_query(F.data == "auth_profile_skip_description")
@admin_required
@error_handler
async def callback_skip_description(callback: CallbackQuery, state: FSMContext):
    """Skip description and move to authentication type selection"""
    await state.update_data(description="")
    await _show_auth_type_selection(callback, state)


@router.message(AuthProfileStates.WAITING_DESCRIPTION)
@admin_required
@error_handler
async def message_description(message: Message, state: FSMContext):
    """Handle description input"""
    description = message.text.strip()

    if description.lower() == "skip":
        description = ""

    await state.update_data(description=description)
    await _show_auth_type_selection(message, state, is_callback=False)


async def _show_auth_type_selection(
    message_or_callback, state: FSMContext, is_callback: bool = True
):
    """Show authentication type selection"""
    await state.set_state(AuthProfileStates.WAITING_AUTH_TYPE)

    data = await state.get_data()

    text = f"✅ Profile name: <code>{data['profile_name']}</code>\n"
    text += f"✅ Display name: {_html.escape(data['display_name'])}\n"
    if data.get("description"):
        text += f"✅ Description: {_html.escape(data['description'])}\n"
    text += "\n"

    text += "🔐 <b>Step 4:</b> Select authentication type\n\n"
    text += "Choose the type of authentication this profile will use:\n\n"

    auth_types = [
        (AuthenticationType.API_KEY, "🔑", "API Key", "Simple API key authentication"),
        (
            AuthenticationType.BEARER_TOKEN,
            "🎫",
            "Bearer Token",
            "JWT or bearer token authentication",
        ),
        (
            AuthenticationType.BASIC_AUTH,
            "🔐",
            "Basic Auth",
            "Username and password authentication",
        ),
        (AuthenticationType.OAUTH2, "🔒", "OAuth2", "OAuth2 flow authentication"),
        (
            AuthenticationType.CUSTOM_HEADER,
            "📋",
            "Custom Header",
            "Custom header-based authentication",
        ),
    ]

    keyboard = []
    for auth_type, icon, name, description in auth_types:
        text += f"{icon} <b>{name}:</b> {description}\n"
        keyboard.append(
            [
                InlineKeyboardButton(
                    text=f"{icon} {name}",
                    callback_data=f"auth_type_select:{auth_type.value}",
                )
            ]
        )

    keyboard.append(
        [InlineKeyboardButton(text="❌ Cancel", callback_data="auth_profiles_main")]
    )

    reply_markup = InlineKeyboardMarkup(inline_keyboard=keyboard)

    if is_callback:
        await message_or_callback.message.edit_text(
            text, parse_mode="HTML", reply_markup=reply_markup
        )
    else:
        await message_or_callback.answer(
            text, parse_mode="HTML", reply_markup=reply_markup
        )


@router.callback_query(F.data.startswith("auth_type_select:"))
@admin_required
@error_handler
async def callback_auth_type_select(callback: CallbackQuery, state: FSMContext):
    """Handle authentication type selection"""
    auth_type_value = callback.data.split(":", 1)[1]
    auth_type = AuthenticationType(auth_type_value)

    await state.update_data(auth_type=auth_type)

    # Show credential input based on auth type
    if auth_type == AuthenticationType.API_KEY:
        await _show_api_key_input(callback, state)
    elif auth_type == AuthenticationType.BEARER_TOKEN:
        await _show_bearer_token_input(callback, state)
    else:
        # For other auth types, show a simplified credential input
        await _show_generic_credential_input(callback, state, auth_type)


async def _show_api_key_input(callback: CallbackQuery, state: FSMContext):
    """Show API key input"""
    await state.set_state(AuthProfileStates.WAITING_API_KEY)

    data = await state.get_data()

    text = f"✅ Profile: {_html.escape(data['display_name'])}\n"
    text += f"✅ Auth Type: 🔑 API Key\n\n"
    text += "🔑 <b>Step 5:</b> Enter API Key\n\n"
    text += "Please enter the API key for this authentication profile.\n\n"
    text += (
        "⚠️ <b>Security Note:</b> The API key will be encrypted and stored securely.\n\n"
    )
    text += "Enter the API key:"

    keyboard = [
        [InlineKeyboardButton(text="❌ Cancel", callback_data="auth_profiles_main")]
    ]

    await callback.message.edit_text(
        text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )


async def _show_bearer_token_input(callback: CallbackQuery, state: FSMContext):
    """Show bearer token input"""
    await state.set_state(AuthProfileStates.WAITING_BEARER_TOKEN)

    data = await state.get_data()

    text = f"✅ Profile: {_html.escape(data['display_name'])}\n"
    text += f"✅ Auth Type: 🎫 Bearer Token\n\n"
    text += "🎫 <b>Step 5:</b> Enter Bearer Token\n\n"
    text += "Please enter the bearer token (JWT or similar) for this authentication profile.\n\n"
    text += (
        "⚠️ <b>Security Note:</b> The token will be encrypted and stored securely.\n\n"
    )
    text += "Enter the bearer token:"

    keyboard = [
        [InlineKeyboardButton(text="❌ Cancel", callback_data="auth_profiles_main")]
    ]

    await callback.message.edit_text(
        text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )


async def _show_generic_credential_input(
    callback: CallbackQuery, state: FSMContext, auth_type: AuthenticationType
):
    """Show generic credential input for other auth types"""
    await state.set_state(AuthProfileStates.WAITING_CREDENTIALS)

    data = await state.get_data()

    auth_type_names = {
        AuthenticationType.BASIC_AUTH: "🔐 Basic Auth",
        AuthenticationType.OAUTH2: "🔒 OAuth2",
        AuthenticationType.CUSTOM_HEADER: "📋 Custom Header",
    }

    text = f"✅ Profile: {_html.escape(data['display_name'])}\n"
    text += f"✅ Auth Type: {auth_type_names.get(auth_type, auth_type.value)}\n\n"
    text += f"🔐 <b>Step 5:</b> Enter Credentials\n\n"

    if auth_type == AuthenticationType.BASIC_AUTH:
        text += "Please enter credentials in the format: username:password\n\n"
        text += "Example: myuser:mypassword\n\n"
    elif auth_type == AuthenticationType.OAUTH2:
        text += "Please enter OAuth2 credentials in JSON format:\n\n"
        text += "Example:\n"
        text += "{\n"
        text += '  "oauth2_client_id": "your_client_id",\n'
        text += '  "oauth2_client_secret": "your_client_secret",\n'
        text += '  "oauth2_token_url": "https://api.example.com/oauth/token"\n'
        text += "}\n\n"
    else:
        text += "Please enter custom authentication data in JSON format.\n\n"

    text += "⚠️ <b>Security Note:</b> Sensitive data will be encrypted and stored securely.\n\n"
    text += "Enter the credentials:"

    keyboard = [
        [InlineKeyboardButton(text="❌ Cancel", callback_data="auth_profiles_main")]
    ]

    await callback.message.edit_text(
        text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )


@router.message(AuthProfileStates.WAITING_API_KEY)
@admin_required
@error_handler
async def message_api_key(message: Message, state: FSMContext):
    """Handle API key input"""
    api_key = message.text.strip()

    if not api_key:
        await message.answer("❌ API key cannot be empty.")
        return

    # Store credentials and create profile
    credentials = {"api_key": api_key}
    await _create_profile_with_credentials(message, state, credentials)


@router.message(AuthProfileStates.WAITING_BEARER_TOKEN)
@admin_required
@error_handler
async def message_bearer_token(message: Message, state: FSMContext):
    """Handle bearer token input"""
    bearer_token = message.text.strip()

    if not bearer_token:
        await message.answer("❌ Bearer token cannot be empty.")
        return

    # Store credentials and create profile
    credentials = {"bearer_token": bearer_token, "login_token": bearer_token}
    await _create_profile_with_credentials(message, state, credentials)


@router.message(AuthProfileStates.WAITING_CREDENTIALS)
@admin_required
@error_handler
async def message_credentials(message: Message, state: FSMContext):
    """Handle generic credentials input"""
    credentials_text = message.text.strip()

    if not credentials_text:
        await message.answer("❌ Credentials cannot be empty.")
        return

    data = await state.get_data()
    auth_type = data["auth_type"]

    try:
        if auth_type == AuthenticationType.BASIC_AUTH:
            # Parse username:password format
            if ":" not in credentials_text:
                await message.answer("❌ Invalid format. Please use: username:password")
                return

            username, password = credentials_text.split(":", 1)
            credentials = {"username": username.strip(), "password": password.strip()}

        elif auth_type == AuthenticationType.OAUTH2:
            # Parse JSON format
            credentials = json.loads(credentials_text)

        else:  # CUSTOM_HEADER
            # Parse JSON format
            credentials = json.loads(credentials_text)

        await _create_profile_with_credentials(message, state, credentials)

    except json.JSONDecodeError:
        await message.answer(
            "❌ Invalid JSON format. Please check your input and try again."
        )
    except Exception as e:
        await message.answer(f"❌ Error parsing credentials: {str(e)}")


async def _create_profile_with_credentials(
    message: Message, state: FSMContext, credentials: Dict[str, Any]
):
    """Create authentication profile with provided credentials"""
    try:
        data = await state.get_data()
        auth_service = get_auth_profile_service()
        user_id = str(message.from_user.id)

        # Create the profile
        profile = await auth_service.create_profile(
            profile_name=data["profile_name"],
            display_name=data["display_name"],
            auth_type=data["auth_type"],
            credentials=credentials,
            created_by=user_id,
            description=data.get("description", ""),
            scope=AuthProfileScope.GLOBAL,
            environment=APIEnvironment.DEVELOPMENT,
        )

        if profile:
            text = "✅ <b>Authentication Profile Created Successfully!</b>\n\n"
            text += f"📝 <b>Profile:</b> {_html.escape(profile.display_name)}\n"
            text += f"🔐 <b>Type:</b> {profile.auth_type.value.title()}\n"
            text += f"🆔 <b>ID:</b> <code>{profile.id}</code>\n\n"
            text += "🎉 Your authentication profile is now ready to be assigned to API configurations!\n\n"
            text += "💡 <b>Next Steps:</b>\n"
            text += "• Assign this profile to API configurations\n"
            text += "• Test the profile with your APIs\n"
            text += "• Configure any necessary overrides"

            keyboard = [
                [
                    InlineKeyboardButton(
                        text="👀 View Profile",
                        callback_data=f"auth_profile_view:{profile.id}",
                    ),
                    InlineKeyboardButton(
                        text="🔗 Assign to API",
                        callback_data=f"auth_profile_assign:{profile.id}",
                    ),
                ],
                [
                    InlineKeyboardButton(
                        text="🏠 Back to Profiles", callback_data="auth_profiles_main"
                    )
                ],
            ]

            await message.answer(
                text,
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
            )
        else:
            await message.answer(
                "❌ Failed to create authentication profile. Please try again.",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🔄 Try Again", callback_data="auth_profile_create"
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🏠 Back to Profiles",
                                callback_data="auth_profiles_main",
                            )
                        ],
                    ]
                ),
            )

        await state.clear()

    except Exception as e:
        logger.error(f"Failed to create authentication profile: {e}")
        await message.answer(
            f"❌ Error creating profile: {str(e)}",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🏠 Back to Profiles",
                            callback_data="auth_profiles_main",
                        )
                    ]
                ]
            ),
        )
        await state.clear()


# Profile assignment handlers


@router.callback_query(F.data.startswith("auth_profile_assign:"))
@admin_required
@error_handler
async def callback_assign_profile(callback: CallbackQuery, state: FSMContext):
    """Show API selection for profile assignment"""
    profile_id = callback.data.split(":", 1)[1]

    # Get available API configurations
    api_service = get_api_config_service()
    all_configs = await api_service.get_all_configurations()

    if not all_configs:
        await callback.answer("❌ No API configurations found", show_alert=True)
        return

    # Store profile ID for later use
    await state.update_data(assign_profile_id=profile_id)
    await state.set_state(AuthProfileStates.WAITING_PROFILE_SELECTION)

    text = "🔗 <b>Assign Authentication Profile</b>\n\n"
    text += "Select an API configuration to assign this authentication profile to:\n\n"

    keyboard = []
    for service_name, config in all_configs.items():
        status = "🟢" if config.enabled else "🔴"
        profile_status = "🔗" if config.credentials.use_profile else "🔓"

        text += f"{status} {profile_status} {_html.escape(config.display_name or service_name)}\n"

        keyboard.append(
            [
                InlineKeyboardButton(
                    text=f"{status} {config.display_name or service_name}",
                    callback_data=f"assign_to_api:{service_name}",
                )
            ]
        )

    keyboard.append(
        [
            InlineKeyboardButton(
                text="❌ Cancel", callback_data=f"auth_profile_view:{profile_id}"
            )
        ]
    )

    await callback.message.edit_text(
        text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )


@router.callback_query(F.data.startswith("assign_to_api:"))
@admin_required
@error_handler
async def callback_assign_to_api(callback: CallbackQuery, state: FSMContext):
    """Assign profile to selected API configuration"""
    service_name = callback.data.split(":", 1)[1]
    data = await state.get_data()
    profile_id = data.get("assign_profile_id")

    if not profile_id:
        await callback.answer("❌ Profile ID not found", show_alert=True)
        return

    # Perform the assignment
    api_service = get_api_config_service()
    user_id = str(callback.from_user.id)

    success = await api_service.assign_auth_profile(
        service_name=service_name, auth_profile_id=profile_id, user_id=user_id
    )

    if success:
        text = "✅ <b>Profile Assigned Successfully!</b>\n\n"
        text += (
            f"🔗 Authentication profile has been assigned to <b>{service_name}</b>\n\n"
        )
        text += (
            "💡 The API configuration will now use the shared authentication profile.\n"
        )
        text += "Any changes to the profile will automatically propagate to this API."

        keyboard = [
            [
                InlineKeyboardButton(
                    text="👀 View API Config",
                    callback_data=f"api_config_view:{service_name}",
                ),
                InlineKeyboardButton(
                    text="👀 View Profile",
                    callback_data=f"auth_profile_view:{profile_id}",
                ),
            ],
            [
                InlineKeyboardButton(
                    text="🏠 Back to Profiles", callback_data="auth_profiles_main"
                )
            ],
        ]
    else:
        text = "❌ <b>Assignment Failed</b>\n\n"
        text += (
            "Failed to assign the authentication profile to the API configuration.\n"
        )
        text += "Please check the logs for more details."

        keyboard = [
            [
                InlineKeyboardButton(
                    text="🔄 Try Again",
                    callback_data=f"auth_profile_assign:{profile_id}",
                )
            ],
            [
                InlineKeyboardButton(
                    text="🏠 Back to Profiles", callback_data="auth_profiles_main"
                )
            ],
        ]

    await callback.message.edit_text(
        text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )

    await state.clear()
