"""
Telegram bot handlers setup
"""

from __future__ import annotations

import logging
from aiogram import Dispatcher

from handlers.user_handlers import get_user_router
from handlers.wallet_handlers import get_wallet_router
from handlers.catalog_handlers import get_catalog_router
from handlers.admin_handlers import get_admin_router
from handlers.history_handlers import get_history_router
from handlers.cart_handlers import get_cart_router
from handlers.purchase_handlers import get_purchase_router

logger = logging.getLogger(__name__)


def setup_handlers(dp: Dispatcher) -> None:
    """Setup all bot handlers"""
    try:
        # Include feature routers (aiogram 3.x best practice)
        dp.include_router(get_user_router())
        dp.include_router(get_wallet_router())
        dp.include_router(get_catalog_router())
        dp.include_router(get_cart_router())
        dp.include_router(get_purchase_router())
        # Include general admin (contains API Configuration UI)
        dp.include_router(get_admin_router())
        dp.include_router(get_history_router())

        logger.info("All handlers setup completed")

    except Exception as e:
        logger.error(f"Failed to setup handlers: {e}")
        raise
