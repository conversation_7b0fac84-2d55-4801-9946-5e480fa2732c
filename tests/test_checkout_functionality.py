#!/usr/bin/env python3
"""
Comprehensive test for checkout functionality
Tests the complete checkout flow including error handling
"""

import asyncio
import logging
import sys
from typing import Dict, Any
from unittest.mock import AsyncMock, patch

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_checkout_api_endpoint():
    """Test the checkout API endpoint directly"""
    logger.info("🧪 Testing checkout API endpoint...")
    
    try:
        import requests
        
        # Test configuration
        url = "https://ronaldo-club.to/api/cart/checkout"
        headers = {
            "accept": "application/json, text/plain, */*",
            "accept-language": "en-US,en;q=0.9",
            "origin": "https://ronaldo-club.to",
            "referer": "https://ronaldo-club.to/store/cart",
            "user-agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Mobile Safari/537.36",
        }
        
        # Use test token
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTk5MDk0LCJpYXQiOjE3NTczNTQ3NDYsImV4cCI6MTc1OTk0Njc0Nn0.iXL_3zO-VJ2elJ4furlJ_FVSZONnRwogtQdZofX3M6o"
        headers["Authorization"] = f"Bearer {token}"
        
        cookies = {
            "__ddg1_": "u1UaBqLkngSC4ZTJRDQC",
            "testcookie": "1",
            "loginToken": token,
            "__ddg9_": "117.199.242.151",
            "__ddg8_": "DpKwxr3KiBBqnvBd",
            "__ddg10_": "1757363464",
        }
        
        # Test GET request
        resp = requests.get(url, headers=headers, cookies=cookies, timeout=30)
        
        logger.info(f"✅ API Response:")
        logger.info(f"   Status: {resp.status_code}")
        logger.info(f"   Content-Type: {resp.headers.get('Content-Type', 'N/A')}")
        
        # Parse response
        try:
            if "application/json" in resp.headers.get("Content-Type", ""):
                data = resp.json()
                logger.info(f"   JSON Response: {data}")
                
                # Analyze response
                if isinstance(data, dict):
                    success = data.get("success")
                    message = data.get("message", "")
                    
                    if success is True:
                        logger.info("✅ Checkout would succeed (user has sufficient balance)")
                        return True, "success", data
                    elif success is False:
                        if "insufficient balance" in message.lower():
                            logger.info("✅ Checkout correctly identifies insufficient balance")
                            return True, "insufficient_balance", data
                        else:
                            logger.info(f"✅ Checkout correctly identifies error: {message}")
                            return True, "business_error", data
                    else:
                        logger.warning(f"⚠️ Unexpected response format: {data}")
                        return False, "unexpected_format", data
                else:
                    logger.warning(f"⚠️ Response is not a dictionary: {data}")
                    return False, "invalid_format", data
            else:
                logger.info(f"   Text Response: {resp.text[:500]}")
                return False, "non_json_response", {"text": resp.text}
                
        except Exception as e:
            logger.error(f"❌ Failed to parse response: {e}")
            return False, "parse_error", {"error": str(e)}
            
    except Exception as e:
        logger.error(f"❌ API test failed: {e}")
        return False, "request_error", {"error": str(e)}


async def test_checkout_queue_service():
    """Test the checkout queue service error handling"""
    logger.info("🧪 Testing checkout queue service...")
    
    try:
        from services.checkout_queue_service import CheckoutQueueService
        
        # Create service instance
        service = CheckoutQueueService()
        
        # Test the _execute_external_checkout method with mocked response
        with patch('aiohttp.ClientSession.get') as mock_get:
            # Mock a 405 response with insufficient balance
            mock_response = AsyncMock()
            mock_response.status = 405
            mock_response.json.return_value = {
                "success": False,
                "message": "Insufficient Balance"
            }
            mock_response.headers = {"Content-Type": "application/json"}
            
            mock_get.return_value.__aenter__.return_value = mock_response
            
            # Create a mock session
            mock_session = AsyncMock()
            
            # Test the method
            result = await service._execute_external_checkout(mock_session)
            
            logger.info(f"✅ Checkout service result: {result}")
            
            # Verify error handling
            if not result.get("success"):
                message = result.get("message", "")
                if "insufficient balance" in message.lower():
                    logger.info("✅ Service correctly handles insufficient balance error")
                    return True, "insufficient_balance_handled", result
                else:
                    logger.info(f"✅ Service handles error: {message}")
                    return True, "error_handled", result
            else:
                logger.warning("⚠️ Expected failure but got success")
                return False, "unexpected_success", result
                
    except Exception as e:
        logger.error(f"❌ Checkout queue service test failed: {e}")
        return False, "service_error", {"error": str(e)}


async def test_user_balance_check():
    """Test user balance checking"""
    logger.info("🧪 Testing user balance check...")
    
    try:
        import requests
        
        # Get user info to check balance
        url = "https://ronaldo-club.to/api/user/getme"
        headers = {
            "accept": "application/json, text/plain, */*",
            "user-agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Mobile Safari/537.36",
        }
        
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTk5MDk0LCJpYXQiOjE3NTczNTQ3NDYsImV4cCI6MTc1OTk0Njc0Nn0.iXL_3zO-VJ2elJ4furlJ_FVSZONnRwogtQdZofX3M6o"
        headers["Authorization"] = f"Bearer {token}"
        
        cookies = {
            "__ddg1_": "u1UaBqLkngSC4ZTJRDQC",
            "testcookie": "1",
            "loginToken": token,
        }
        
        resp = requests.get(url, headers=headers, cookies=cookies, timeout=30)
        
        if resp.status_code == 200:
            data = resp.json()
            if data.get("success") and "user" in data:
                user = data["user"]
                balance = float(user.get("balance", "0"))
                logger.info(f"✅ User balance: ${balance:.2f}")
                
                # Get cart total
                cart_url = "https://ronaldo-club.to/api/cart/"
                cart_resp = requests.get(cart_url, headers=headers, cookies=cookies, timeout=30)
                
                if cart_resp.status_code == 200:
                    cart_data = cart_resp.json()
                    if cart_data.get("success"):
                        cart_total = float(cart_data.get("totalCartPrice", 0))
                        logger.info(f"✅ Cart total: ${cart_total:.2f}")
                        
                        if balance >= cart_total:
                            logger.info("✅ User has sufficient balance for checkout")
                            return True, "sufficient_balance", {"balance": balance, "cart_total": cart_total}
                        else:
                            logger.info("✅ User has insufficient balance (expected for test)")
                            return True, "insufficient_balance", {"balance": balance, "cart_total": cart_total}
                    else:
                        logger.warning("⚠️ Failed to get cart data")
                        return False, "cart_error", cart_data
                else:
                    logger.warning(f"⚠️ Cart request failed: {cart_resp.status_code}")
                    return False, "cart_request_failed", {"status": cart_resp.status_code}
            else:
                logger.warning("⚠️ Invalid user data response")
                return False, "invalid_user_data", data
        else:
            logger.warning(f"⚠️ User info request failed: {resp.status_code}")
            return False, "user_request_failed", {"status": resp.status_code}
            
    except Exception as e:
        logger.error(f"❌ Balance check failed: {e}")
        return False, "balance_check_error", {"error": str(e)}


async def main():
    """Run all checkout tests"""
    logger.info("🚀 Starting comprehensive checkout functionality tests...")
    
    tests = [
        ("API Endpoint Test", test_checkout_api_endpoint),
        ("Checkout Queue Service Test", test_checkout_queue_service),
        ("User Balance Check", test_user_balance_check),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            success, result_type, data = await test_func()
            results.append((test_name, success, result_type, data))
            
            if success:
                logger.info(f"✅ {test_name}: PASSED ({result_type})")
            else:
                logger.error(f"❌ {test_name}: FAILED ({result_type})")
                
        except Exception as e:
            logger.error(f"❌ {test_name}: EXCEPTION - {e}")
            results.append((test_name, False, "exception", {"error": str(e)}))
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for _, success, _, _ in results if success)
    total = len(results)
    
    for test_name, success, result_type, _ in results:
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} {test_name} ({result_type})")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Checkout functionality is working correctly.")
        return True
    else:
        logger.error("⚠️ Some tests failed. Please review the issues above.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
