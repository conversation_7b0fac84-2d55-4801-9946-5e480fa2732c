#!/bin/bash
# Setup script for Demo Wallet Bot v2
# This script sets up the proper Python environment and dependencies

set -e  # Exit on any error

echo "🚀 Setting up Demo Wallet Bot v2..."

# Check Python version
python_version=$(python3 --version 2>&1 | cut -d' ' -f2 | cut -d'.' -f1,2)
required_version="3.11"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Python $required_version or higher is required. Found: $python_version"
    exit 1
fi

echo "✅ Python version check passed: $python_version"

# Remove old Windows virtual environment if it exists
if [ -d "venv" ] && [ -f "venv/pyvenv.cfg" ]; then
    if grep -q "Windows" venv/pyvenv.cfg 2>/dev/null || grep -q "C:\\" venv/pyvenv.cfg 2>/dev/null; then
        echo "🧹 Removing incompatible Windows virtual environment..."
        rm -rf venv
    fi
fi

# Check if we have a proper Linux virtual environment
if [ ! -f "venv/bin/activate" ]; then
    if [ -d "venv" ]; then
        echo "🧹 Removing incompatible virtual environment..."
        rm -rf venv
    fi
    echo "📦 Creating Python virtual environment..."
    python3 -m venv venv
else
    echo "✅ Virtual environment already exists"
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "⬆️  Upgrading pip..."
pip install --upgrade pip

# Install dependencies
echo "📥 Installing dependencies..."
pip install -r requirements.txt

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "📝 Creating .env file from template..."
    cp config.example.env .env
    echo ""
    echo "⚠️  IMPORTANT: Edit .env file and set your BOT_TOKEN"
    echo "   Get your bot token from @BotFather on Telegram"
    echo ""
fi

# Verify setup by testing imports
echo "🧪 Running setup verification..."
python -c "
import sys
try:
    from main import BotApplication
    from config.settings import load_settings
    from services.user_service import UserService
    print('✅ All critical imports successful')
    print('✅ Setup verification completed')
except ImportError as e:
    print(f'❌ Import failed: {e}')
    sys.exit(1)
except Exception as e:
    print(f'❌ Setup verification failed: {e}')
    sys.exit(1)
"

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📋 Next steps:"
echo "   1. Edit .env file and set your BOT_TOKEN"
echo "   2. Run the bot: ./run.sh"
echo ""
echo "📚 For more information, see README.md"
