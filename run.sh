#!/bin/bash
# Run script for Demo Wallet Bot v2
# This script activates the virtual environment and runs the bot

set -e  # Exit on any error

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "❌ Virtual environment not found. Run ./setup.sh first"
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Copy config.example.env to .env and configure it"
    exit 1
fi

# Check if BOT_TOKEN is set
if ! grep -q "^BOT_TOKEN=.*[^[:space:]]" .env; then
    echo "❌ BOT_TOKEN not set in .env file. Get your token from @BotFather"
    exit 1
fi

echo "🚀 Starting Demo Wallet Bot v2..."

# Activate virtual environment and run the bot
source venv/bin/activate
python run.py
