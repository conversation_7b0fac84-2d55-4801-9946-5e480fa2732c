"""
Performance optimization utilities for the Demo Wallet Bot
"""

from __future__ import annotations

import asyncio
import time
from functools import wraps
from typing import Any, Callable, Dict, Optional, TypeVar, Union
import logging

logger = logging.getLogger(__name__)

F = TypeVar('F', bound=Callable[..., Any])


def async_cache(ttl_seconds: int = 300):
    """
    Simple async function cache decorator with TTL
    
    Args:
        ttl_seconds: Time to live in seconds
    """
    def decorator(func: F) -> F:
        cache: Dict[str, tuple[Any, float]] = {}
        
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Create cache key
            key = f"{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            current_time = time.time()
            
            # Check cache
            if key in cache:
                value, timestamp = cache[key]
                if current_time - timestamp < ttl_seconds:
                    return value
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            cache[key] = (result, current_time)
            
            # Clean old entries periodically
            if len(cache) > 100:  # Prevent memory bloat
                expired_keys = [
                    k for k, (_, ts) in cache.items()
                    if current_time - ts >= ttl_seconds
                ]
                for k in expired_keys:
                    cache.pop(k, None)
            
            return result
        
        return wrapper
    return decorator


def batch_database_operations(batch_size: int = 100):
    """
    Decorator to batch database operations for better performance
    
    Args:
        batch_size: Number of operations to batch together
    """
    def decorator(func: F) -> F:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # This is a placeholder for batching logic
            # In a real implementation, you'd collect operations and execute them in batches
            return await func(*args, **kwargs)
        return wrapper
    return decorator


class QueryOptimizer:
    """Utility class for optimizing database queries"""
    
    @staticmethod
    def build_user_search_query(query: str) -> Dict[str, Any]:
        """
        Build optimized MongoDB query for user search
        
        Args:
            query: Search query string
            
        Returns:
            MongoDB query dictionary
        """
        if not query or not query.strip():
            return {}
        
        query = query.strip()
        
        # If query is numeric, search by telegram_id
        if query.isdigit():
            return {"telegram_id": int(query)}
        
        # Text search using regex (case-insensitive)
        regex_pattern = {"$regex": query, "$options": "i"}
        return {
            "$or": [
                {"username": regex_pattern},
                {"first_name": regex_pattern}
            ]
        }
    
    @staticmethod
    def build_transaction_query(
        user_id: str,
        transaction_type: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Build optimized query for transaction history
        
        Args:
            user_id: User ID to filter by
            transaction_type: Optional transaction type filter
            start_date: Optional start date filter
            end_date: Optional end date filter
            
        Returns:
            MongoDB query dictionary
        """
        query = {"user_id": user_id}
        
        if transaction_type:
            query["type"] = transaction_type
        
        if start_date or end_date:
            date_filter = {}
            if start_date:
                date_filter["$gte"] = start_date
            if end_date:
                date_filter["$lte"] = end_date
            query["created_at"] = date_filter
        
        return query
    
    @staticmethod
    def build_catalog_filter_query(filters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Build optimized query for catalog filtering
        
        Args:
            filters: Filter criteria dictionary
            
        Returns:
            MongoDB query dictionary
        """
        query = {"active": True}  # Only show active items
        
        for key, value in filters.items():
            if not value:
                continue
                
            if key == "price_min":
                query.setdefault("price", {})["$gte"] = float(value)
            elif key == "price_max":
                query.setdefault("price", {})["$lte"] = float(value)
            elif key == "country":
                query["country"] = value.upper()
            elif key == "brand":
                query["brand"] = value.upper()
            elif key == "type":
                query["type"] = value.upper()
            elif key == "bin":
                query["bin"] = {"$regex": f"^{value}"}
        
        return query


class PerformanceMonitor:
    """Monitor and log performance metrics"""
    
    def __init__(self):
        self.query_times: Dict[str, list[float]] = {}
    
    def log_query_time(self, operation: str, duration: float):
        """Log query execution time"""
        if operation not in self.query_times:
            self.query_times[operation] = []
        
        self.query_times[operation].append(duration)
        
        # Keep only last 100 measurements
        if len(self.query_times[operation]) > 100:
            self.query_times[operation] = self.query_times[operation][-100:]
        
        # Log slow queries
        if duration > 1.0:  # Queries taking more than 1 second
            logger.warning(f"Slow query detected: {operation} took {duration:.2f}s")
    
    def get_average_time(self, operation: str) -> float:
        """Get average query time for an operation"""
        times = self.query_times.get(operation, [])
        return sum(times) / len(times) if times else 0.0
    
    def get_stats(self) -> Dict[str, Dict[str, float]]:
        """Get performance statistics"""
        stats = {}
        for operation, times in self.query_times.items():
            if times:
                stats[operation] = {
                    "avg_time": sum(times) / len(times),
                    "max_time": max(times),
                    "min_time": min(times),
                    "count": len(times)
                }
        return stats


# Global performance monitor instance
perf_monitor = PerformanceMonitor()


def monitor_performance(operation_name: str):
    """
    Decorator to monitor function performance
    
    Args:
        operation_name: Name of the operation for logging
    """
    def decorator(func: F) -> F:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                perf_monitor.log_query_time(operation_name, duration)
        
        return wrapper
    return decorator


async def optimize_collection_query(collection, query: Dict[str, Any], limit: int = 100) -> list:
    """
    Execute optimized collection query with performance monitoring
    
    Args:
        collection: MongoDB collection
        query: Query dictionary
        limit: Maximum number of results
        
    Returns:
        List of documents
    """
    start_time = time.time()
    try:
        # Use projection to limit returned fields if possible
        cursor = collection.find(query).limit(limit)
        results = await cursor.to_list(length=limit)
        
        duration = time.time() - start_time
        perf_monitor.log_query_time("collection_query", duration)
        
        return results
    except Exception as e:
        logger.error(f"Query optimization failed: {e}")
        raise


def create_compound_index_hint(fields: list[str]) -> Dict[str, int]:
    """
    Create index hint for compound indexes
    
    Args:
        fields: List of field names for the index
        
    Returns:
        Index hint dictionary
    """
    return {field: 1 for field in fields}
