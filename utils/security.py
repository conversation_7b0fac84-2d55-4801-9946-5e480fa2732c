"""
Security utilities for the Demo Wallet Bot
"""

from __future__ import annotations

import hashlib
import hmac
import secrets
import time
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)


class SecurityError(Exception):
    """Custom security error"""

    pass


def generate_secure_token(length: int = 32) -> str:
    """Generate a cryptographically secure random token"""
    return secrets.token_urlsafe(length)


def hash_sensitive_data(data: str, salt: Optional[str] = None) -> str:
    """
    Hash sensitive data with optional salt

    Args:
        data: Data to hash
        salt: Optional salt (generates random if not provided)

    Returns:
        Hashed data as hex string
    """
    if salt is None:
        salt = secrets.token_hex(16)

    return hashlib.pbkdf2_hmac("sha256", data.encode(), salt.encode(), 100000).hex()


def verify_integrity_hash(
    data: Dict[str, Any], expected_hash: str, secret_key: str
) -> bool:
    """
    Verify data integrity using HMAC

    Args:
        data: Data to verify
        expected_hash: Expected hash value
        secret_key: Secret key for HMAC

    Returns:
        True if hash is valid
    """
    try:
        import json

        content = json.dumps(data, sort_keys=True)
        computed_hash = hmac.new(
            secret_key.encode(), content.encode(), hashlib.sha256
        ).hexdigest()

        return hmac.compare_digest(expected_hash, computed_hash)
    except Exception as e:
        logger.warning(f"Hash verification failed: {e}")
        return False


def check_rate_limit_security(
    user_id: int, action: str, max_attempts: int = 5, window_seconds: int = 300
) -> bool:
    """
    Enhanced rate limiting for security-sensitive actions

    Args:
        user_id: User ID
        action: Action being performed
        max_attempts: Maximum attempts allowed
        window_seconds: Time window in seconds

    Returns:
        True if action is allowed, False if rate limited
    """
    # Simple in-memory tracking (in production, use Redis or database)
    if not hasattr(check_rate_limit_security, "_attempts"):
        check_rate_limit_security._attempts = {}

    key = f"{user_id}:{action}"
    current_time = time.time()

    # Clean old entries
    if key in check_rate_limit_security._attempts:
        check_rate_limit_security._attempts[key] = [
            timestamp
            for timestamp in check_rate_limit_security._attempts[key]
            if current_time - timestamp < window_seconds
        ]
    else:
        check_rate_limit_security._attempts[key] = []

    # Check if limit exceeded
    if len(check_rate_limit_security._attempts[key]) >= max_attempts:
        logger.warning(f"Rate limit exceeded for user {user_id} action {action}")
        return False

    # Record attempt
    check_rate_limit_security._attempts[key].append(current_time)
    return True


def sanitize_admin_input(input_text: str, field_name: str) -> str:
    """
    Sanitize admin input with strict validation

    Args:
        input_text: Input to sanitize
        field_name: Name of the field for error messages

    Returns:
        Sanitized input

    Raises:
        SecurityError: If input is potentially dangerous
    """
    if not isinstance(input_text, str):
        raise SecurityError(f"Invalid {field_name}: must be text")

    # Basic length check
    if len(input_text) > 1000:
        raise SecurityError(f"{field_name} too long")

    # Check for potentially dangerous patterns
    dangerous_patterns = [
        r"<script[^>]*>",
        r"javascript:",
        r"data:text/html",
        r"vbscript:",
        r"onload\s*=",
        r"onerror\s*=",
        r"eval\s*\(",
        r"exec\s*\(",
    ]

    import re

    for pattern in dangerous_patterns:
        if re.search(pattern, input_text, re.IGNORECASE):
            raise SecurityError(f"Potentially dangerous content in {field_name}")

    from utils.validation import sanitize_text_input

    return sanitize_text_input(input_text)


def hash_password(password: str, salt: Optional[bytes] = None) -> tuple[str, bytes]:
    """
    Hash password using PBKDF2 with SHA-256

    Args:
        password: Plain text password
        salt: Optional salt bytes, generates new if not provided

    Returns:
        Tuple of (hashed_password_hex, salt_bytes)
    """
    import hashlib
    import os

    if salt is None:
        salt = os.urandom(32)  # 256-bit salt

    # Use PBKDF2 with 100,000 iterations
    hashed = hashlib.pbkdf2_hmac("sha256", password.encode("utf-8"), salt, 100000)
    return hashed.hex(), salt


def verify_password(password: str, hashed_password: str, salt: bytes) -> bool:
    """
    Verify password against hash

    Args:
        password: Plain text password to verify
        hashed_password: Stored password hash (hex)
        salt: Salt used for hashing

    Returns:
        True if password matches
    """
    import hashlib
    import hmac

    # Hash the provided password with the same salt
    test_hash = hashlib.pbkdf2_hmac("sha256", password.encode("utf-8"), salt, 100000)

    # Use constant-time comparison to prevent timing attacks
    return hmac.compare_digest(test_hash.hex(), hashed_password)


def validate_admin_session(
    user_id: int, required_permissions: Optional[list] = None
) -> bool:
    """
    Validate admin session with enhanced security checks

    Args:
        user_id: User ID to validate
        required_permissions: List of required permissions

    Returns:
        True if session is valid
    """
    try:
        # Check if user ID is valid
        from utils.validation import validate_telegram_id

        validate_telegram_id(user_id)

        # Check rate limiting for admin actions
        if not check_rate_limit_security(
            user_id, "admin_action", max_attempts=10, window_seconds=60
        ):
            return False

        # Additional permission checks could be added here
        return True

    except Exception as e:
        logger.warning(f"Admin session validation failed for user {user_id}: {e}")
        return False


def log_security_event(
    user_id: int, event_type: str, details: Optional[Dict[str, Any]] = None
):
    """
    Log security-related events for monitoring

    Args:
        user_id: User ID involved in the event
        event_type: Type of security event
        details: Additional event details
    """
    logger.warning(
        f"Security event: {event_type}",
        extra={
            "user_id": user_id,
            "event_type": event_type,
            "details": details or {},
            "timestamp": time.time(),
        },
    )
