import requests

url = "https://ronaldo-club.to/api/user/getme"

headers = {
    "accept": "*/*",
    "accept-language": "en-US,en;q=0.9",
    "content-type": "application/json",
    "priority": "u=1, i",
    "referer": "https://ronaldo-club.to/",
    "sec-ch-ua": '"Chromium";v="140", "Not=A?Brand";v="24", "Brave";v="140"',
    "sec-ch-ua-mobile": "?1",
    "sec-ch-ua-platform": '"Android"',
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "sec-gpc": "1",
    "user-agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) "
    "AppleWebKit/537.36 (KHTML, like Gecko) "
    "Chrome/140.0.0.0 Mobile Safari/537.36",
}

cookies = {
    "__ddg1_": "u1UaBqLkngSC4ZTJRDQC",
    "testcookie": "1",
    "__ddg9_": "118.99.2.11",
    "loginToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9."
    "eyJpZCI6MTk5MDk0LCJpYXQiOjE3NTczMjEzNDgsImV4cCI6MTc1OTkxMzM0OH0."
    "_9FZWkJuYhNuC4cqLMW2JEWH5anNE3lcQkM8NUavXp0",
    "__ddg8_": "sPVsJwi5J1HxWZWW",
    "__ddg10_": "1757321349",
}

# Send GET request
response = requests.get(url, headers=headers, cookies=cookies)

print("Status Code:", response.status_code)
print("Response:", response.text)

# Response
{
    "success": true,
    "user": {
        "_id": 199094,
        "email": "<EMAIL>",
        "telegram_username": null,
        "username": "dcpacheck",
        "rank": "newcomer",
        "role": "user",
        "status": "active",
        "referral_id": "I8daHrem",
        "two_fa_enabled": 0,
        "refferalEarning": "0.0000",
        "balance": "0.00000",
        "wallet": null,
        "earning": "0.0000",
        "createdAt": "2025-09-06T21:02:26.000Z",
        "cartnumber": 2,
        "inbox": 0,
        "ticket": 0,
        "reports": null,
    },
}
