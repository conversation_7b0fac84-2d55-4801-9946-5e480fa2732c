import requests

url = "https://ronaldo-club.to/api/cart/"

headers = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "en-US,en;q=0.9",
    "priority": "u=1, i",
    "referer": "https://ronaldo-club.to/store/cart",
    "sec-ch-ua": '"Chromium";v="140", "Not=A?Brand";v="24", "Brave";v="140"',
    "sec-ch-ua-mobile": "?1",
    "sec-ch-ua-platform": '"Android"',
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "sec-gpc": "1",
    "user-agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) "
    "AppleWebKit/537.36 (KHTML, like Gecko) "
    "Chrome/140.0.0.0 Mobile Safari/537.36",
}

cookies = {
    "__ddg1_": "u1UaBqLkngSC4ZTJRDQC",
    "testcookie": "1",
    "loginToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9."
    "eyJpZCI6MTk5MDk0LCJpYXQiOjE3NTczMjEzNDgsImV4cCI6MTc1OTkxMzM0OH0."
    "_9FZWkJuYhNuC4cqLMW2JEWH5anNE3lcQkM8NUavXp0",
    "__ddg9_": "118.99.2.9",
    "__ddg8_": "lo76QfhQVsTzy1EJ",
    "__ddg10_": "1757323045",
}

# GET request to fetch cart contents
response = requests.get(url, headers=headers, cookies=cookies)

print("Status Code:", response.status_code)
print("Response:", response.text)

# Expected Response format (commented out for reference):
# {
#     "success": true,
#     "data": [...],
#     "totalCartPrice": 44.97
# }





# curl command removed for Python compatibility
