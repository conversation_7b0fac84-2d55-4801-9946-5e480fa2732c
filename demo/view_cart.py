import requests

url = "https://ronaldo-club.to/api/cart/"

headers = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "en-US,en;q=0.9",
    "priority": "u=1, i",
    "referer": "https://ronaldo-club.to/store/cart",
    "sec-ch-ua": '"Chromium";v="140", "Not=A?Brand";v="24", "Brave";v="140"',
    "sec-ch-ua-mobile": "?1",
    "sec-ch-ua-platform": '"Android"',
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "sec-gpc": "1",
    "user-agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) "
    "AppleWebKit/537.36 (KHTML, like Gecko) "
    "Chrome/140.0.0.0 Mobile Safari/537.36",
}

cookies = {
    "__ddg1_": "u1UaBqLkngSC4ZTJRDQC",
    "testcookie": "1",
    "loginToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9."
    "eyJpZCI6MTk5MDk0LCJpYXQiOjE3NTczMjEzNDgsImV4cCI6MTc1OTkxMzM0OH0."
    "_9FZWkJuYhNuC4cqLMW2JEWH5anNE3lcQkM8NUavXp0",
    "__ddg9_": "118.99.2.9",
    "__ddg8_": "lo76QfhQVsTzy1EJ",
    "__ddg10_": "1757323045",
}

# GET request to fetch cart contents
response = requests.get(url, headers=headers, cookies=cookies)

print("Status Code:", response.status_code)
print("Response:", response.text)

# Response
{
    "success": true,
    "data": [
        {
            "_id": 360940,
            "user_id": 199094,
            "product_id": 1773162,
            "product_table_name": "Cards",
            "createdAt": "2025-09-08T09:17:25.000Z",
            "brand": "VISA",
            "bin": "461046",
            "city": "Austin",
            "state": "TX",
            "zip": "78701",
            "exp": "03/30",
            "expyear": null,
            "expmonth": null,
            "country": "US",
            "price": "14.9900",
            "discount": 0,
        },
        {
            "_id": 359879,
            "user_id": 199094,
            "product_id": 1768038,
            "product_table_name": "Cards",
            "createdAt": "2025-09-06T21:16:04.000Z",
            "brand": "VISA",
            "bin": "405037",
            "city": "Skokie",
            "state": "IL",
            "zip": "60076",
            "exp": "07/30",
            "expyear": null,
            "expmonth": null,
            "country": "US",
            "price": "14.9900",
            "discount": 0,
        },
        {
            "_id": 359878,
            "user_id": 199094,
            "product_id": 1768047,
            "product_table_name": "Cards",
            "createdAt": "2025-09-06T21:16:03.000Z",
            "brand": "VISA",
            "bin": "435546",
            "city": "Summerdale",
            "state": "AL",
            "zip": "36580",
            "exp": "05/28",
            "expyear": null,
            "expmonth": null,
            "country": "US",
            "price": "14.9900",
            "discount": 0,
        },
    ],
    "totalCartPrice": 44.97,
}





curl "https://ronaldo-club.to/api/docs/" ^
  -H "accept: application/json, text/plain, */*" ^
  -H "accept-language: en-US,en;q=0.9" ^
  -H "priority: u=1, i" ^
  -H "referer: https://ronaldo-club.to/store/cart" ^
  -H "sec-ch-ua: \"Chromium\";v=\"140\", \"Not=A?Brand\";v=\"24\", \"Brave\";v=\"140\"" ^
  -H "sec-ch-ua-mobile: ?1" ^
  -H "sec-ch-ua-platform: \"Android\"" ^
  -H "sec-fetch-dest: empty" ^
  -H "sec-fetch-mode: cors" ^
  -H "sec-fetch-site: same-origin" ^
  -H "sec-gpc: 1" ^
  -H "user-agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Mobile Safari/537.36" ^
  --cookie "__ddg1_=u1UaBqLkngSC4ZTJRDQC; testcookie=1; loginToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTk5MDk0LCJpYXQiOjE3NTczNTQ3NDYsImV4cCI6MTc1OTk0Njc0Nn0.iXL_3zO-VJ2elJ4furlJ_FVSZONnRwogtQdZofX3M6o; __ddg9_=118.99.2.9; __ddg8_=lo76QfhQVsTzy1EJ; __ddg10_=1757323045"
