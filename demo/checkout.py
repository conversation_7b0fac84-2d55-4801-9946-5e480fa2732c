#!/usr/bin/env python3
"""
Checkout demo aligned with current codebase

Syncs with services/checkout_queue_service.py which uses:
- POST https://ronaldo-club.to/api/checkout/
- Authorization: Bearer <loginToken>
- loginToken cookie + ddg cookies

This script performs a single authenticated POST to the checkout endpoint
and prints status and response body.
"""

import json
import sys
import requests


def main() -> int:
    # Based on testing: GET /cart/checkout returns meaningful response
    # Even though it returns 405, it processes the request and returns business logic errors
    url = "https://ronaldo-club.to/api/cart/checkout"

    # Use the same headers shape as in ExternalAPIService
    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-language": "en-US,en;q=0.9",
        "origin": "https://ronaldo-club.to",
        "priority": "u=1, i",
        "referer": "https://ronaldo-club.to/store/cart",
        "sec-ch-ua": '"Chromium";v="140", "Not=A?Brand";v="24", "Brave";v="140"',
        "sec-ch-ua-mobile": "?1",
        "sec-ch-ua-platform": '"Android"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "sec-gpc": "1",
        "user-agent": (
            "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/140.0.0.0 Mobile Safari/537.36"
        ),
    }

    # Token and cookies (replace with your current, valid values)
    token = (
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9."
        "eyJpZCI6MTk5MDk0LCJpYXQiOjE3NTczNTQ3NDYsImV4cCI6MTc1OTk0Njc0Nn0."
        "iXL_3zO-VJ2elJ4furlJ_FVSZONnRwogtQdZofX3M6o"
    )
    headers["Authorization"] = f"Bearer {token}"

    cookies = {
        "__ddg1_": "u1UaBqLkngSC4ZTJRDQC",
        "testcookie": "1",
        "loginToken": token,
        # ddg cookies — these change periodically; keep them fresh if needed
        "__ddg9_": "117.199.242.151",
        "__ddg8_": "DpKwxr3KiBBqnvBd",
        "__ddg10_": "1757363464",
    }

    try:
        # Use GET method - API returns meaningful response despite 405 status
        resp = requests.get(url, headers=headers, cookies=cookies, timeout=60)
        print("Status:", resp.status_code)
        ct = resp.headers.get("Content-Type", "")
        print("Content-Type:", ct)
        if "application/json" in ct:
            try:
                print("JSON:", json.dumps(resp.json(), ensure_ascii=False)[:2000])
            except Exception:
                print("Body:", resp.text[:2000])
        else:
            print("Body:", resp.text[:2000])
        return 0
    except requests.RequestException as e:
        print("Request failed:", e)
        return 2


if __name__ == "__main__":
    sys.exit(main())
