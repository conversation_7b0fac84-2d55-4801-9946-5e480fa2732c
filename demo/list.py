import requests

url = "https://ronaldo-club.to/api/cards/hq/list?page=1&limit=10&base=&bank=&bin=&country=&state=&city=&brand=&type=&zip=&priceFrom=0&priceTo=500&zipCheck=false&address=false&phone=false&email=false&withoutcvv=false&refundable=false&expirethismonth=false&dob=false&ssn=false&mmn=false&ip=false&dl=false&ua=false&discount=false"

headers = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "en-US,en;q=0.9",
    "content-length": "0",  # not really needed, requests sets automatically
    "origin": "https://ronaldo-club.to",
    "priority": "u=1, i",
    "referer": "https://ronaldo-club.to/store/cards/hq",
    "sec-ch-ua": '"Chromium";v="140", "Not=A?Brand";v="24", "Brave";v="140"',
    "sec-ch-ua-mobile": "?1",
    "sec-ch-ua-platform": '"Android"',
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "sec-gpc": "1",
    "user-agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) "
    "AppleWebKit/537.36 (KHTML, like Gecko) "
    "Chrome/140.0.0.0 Mobile Safari/537.36",
}

cookies = {
    "__ddg1_": "u1UaBqLkngSC4ZTJRDQC",
    "testcookie": "1",
    "loginToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9."
    "eyJpZCI6MTk5MDk0LCJpYXQiOjE3NTczMjEzNDgsImV4cCI6MTc1OTkxMzM0OH0."
    "_9FZWkJuYhNuC4cqLMW2JEWH5anNE3lcQkM8NUavXp0",
    "__ddg8_": "1jEZTHlATJfsDsPH",
    "__ddg10_": "1757322305",
    "__ddg9_": "**************",
}

# POST request (no body, just headers/cookies)
response = requests.post(url, headers=headers, cookies=cookies)

print("Status Code:", response.status_code)
print("Response:", response.text)

# Response
{
    "success": true,
    "data": [
        {
            "_id": 1773162,
            "base": "08SEP_95VR_PHONE_EMAIL_IP_FIRSTHAND",
            "bin": "461046",
            "level": "CLASSIC",
            "type": "DEBIT",
            "exp": "03/30",
            "expmonth": null,
            "expyear": null,
            "city": "Austin",
            "state": "TX",
            "country": "US",
            "zip": "78701",
            "bank": "JPMORGAN CHASE BANK, N.A.",
            "refundable": 0,
            "discount": 0,
            "price": "14.9900",
            "isfirsthand": 1,
            "sellerUsername": "maikejor",
            "address": 1,
            "ip": 1,
            "email": 1,
            "phone": 1,
            "dob": 0,
            "ssn": 0,
            "ua": 0,
            "dl": 0,
            "mmn": 0,
            "basequality": "VeryHigh",
            "refund_rate": "16.31206",
        },
        {
            "_id": 1773173,
            "base": "08SEP_95VR_PHONE_EMAIL_IP_FIRSTHAND",
            "bin": "414709",
            "level": "CLASSIC",
            "type": "CREDIT",
            "exp": "02/28",
            "expmonth": null,
            "expyear": null,
            "city": "Hialeah",
            "state": "FL",
            "country": "US",
            "zip": "33018",
            "bank": "CAPITAL ONE BANK (USA), N.A.",
            "refundable": 0,
            "discount": 0,
            "price": "14.9900",
            "isfirsthand": 1,
            "sellerUsername": "maikejor",
            "address": 1,
            "ip": 1,
            "email": 1,
            "phone": 1,
            "dob": 0,
            "ssn": 0,
            "ua": 0,
            "dl": 0,
            "mmn": 0,
            "basequality": "VeryHigh",
            "refund_rate": "16.31206",
        },
        {
            "_id": 1773170,
            "base": "08SEP_95VR_PHONE_EMAIL_IP_FIRSTHAND",
            "bin": "379571",
            "level": "PERSONAL",
            "type": "CREDIT",
            "exp": "10/29",
            "expmonth": null,
            "expyear": null,
            "city": "Albuquerque",
            "state": "NM",
            "country": "US",
            "zip": "87120",
            "bank": "AMERICAN EXPRESS US CONSUMER",
            "refundable": 0,
            "discount": 0,
            "price": "14.9900",
            "isfirsthand": 1,
            "sellerUsername": "maikejor",
            "address": 1,
            "ip": 1,
            "email": 1,
            "phone": 1,
            "dob": 0,
            "ssn": 0,
            "ua": 0,
            "dl": 0,
            "mmn": 0,
            "basequality": "VeryHigh",
            "refund_rate": "16.31206",
        },
        {
            "_id": 1773169,
            "base": "08SEP_95VR_PHONE_EMAIL_IP_FIRSTHAND",
            "bin": "472409",
            "level": "CLASSIC",
            "type": "DEBIT",
            "exp": "01/27",
            "expmonth": null,
            "expyear": null,
            "city": "Fort St. John",
            "state": "BC",
            "country": "CA",
            "zip": "V1J 4R5",
            "bank": "TD CANADA TRUST",
            "refundable": 0,
            "discount": 0,
            "price": "14.9900",
            "isfirsthand": 1,
            "sellerUsername": "maikejor",
            "address": 1,
            "ip": 1,
            "email": 1,
            "phone": 1,
            "dob": 0,
            "ssn": 0,
            "ua": 0,
            "dl": 0,
            "mmn": 0,
            "basequality": "VeryHigh",
            "refund_rate": "16.31206",
        },
        {
            "_id": 1773164,
            "base": "08SEP_95VR_PHONE_EMAIL_IP_FIRSTHAND",
            "bin": "545941",
            "level": "GOLD",
            "type": "CREDIT",
            "exp": "09/27",
            "expmonth": null,
            "expyear": null,
            "city": "Doral",
            "state": "FL",
            "country": "DO",
            "zip": "33198",
            "bank": "BANCO MULTIPLE BHD LEON, S.A.",
            "refundable": 0,
            "discount": 0,
            "price": "14.9900",
            "isfirsthand": 1,
            "sellerUsername": "maikejor",
            "address": 1,
            "ip": 1,
            "email": 1,
            "phone": 1,
            "dob": 0,
            "ssn": 0,
            "ua": 0,
            "dl": 0,
            "mmn": 0,
            "basequality": "VeryHigh",
            "refund_rate": "16.31206",
        },
    ],
    "totalCount": 114680,
    "limit": 10,
}
