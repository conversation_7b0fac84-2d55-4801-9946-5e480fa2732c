#!/usr/bin/env python3
"""
Seed api1_external_cart configuration from existing external_cart.

This script looks up the legacy API config named 'external_cart' via the
APIConfigService, and if present, creates (or updates) a new config named
'api1_external_cart' with the same base URL, headers, and credentials.

It uses the Pydantic-backed save path, so the new config shows up in the
admin UI and is compatible with the existing services.
"""

from __future__ import annotations

import asyncio
import sys

from database.connection import init_database, close_database
from services.api_config_service import (
    get_api_config_service,
    APIConfiguration,
    ConfigSource,
)


async def seed() -> int:
    await init_database()
    try:
        svc = get_api_config_service()

        # If target already exists, nothing to do
        existing = await svc.get_api_config("api1_external_cart")
        if existing:
            print("api1_external_cart already exists — nothing to seed")
            return 0

        legacy = await svc.get_api_config("external_cart")
        if not legacy:
            print(
                "No legacy 'external_cart' configuration found. Please create it in Admin > APIs or set ENV vars."
            )
            return 1

        # Build a new config with the same values but new service name
        new_cfg = APIConfiguration(
            service_name="api1_external_cart",
            base_url=legacy.base_url,
            endpoints=legacy.endpoints,
            credentials=legacy.credentials,
            enabled=legacy.enabled,
            last_updated=legacy.last_updated,
            source=legacy.source,
            display_name="API 1 - External Cart API",
            description=(
                legacy.description
                or "API 1: External API for cart operations, item listing, and user management (Ronaldo Club)"
            ),
            category=legacy.category or "ecommerce",
            tags=list(set((legacy.tags or []) + ["api1", "external", "cart"]))[:10],
            environment=legacy.environment or "development",
            version=legacy.version or "1.0",
            health_check_endpoint=legacy.health_check_endpoint or "/user/getme",
            documentation_url=legacy.documentation_url or "https://ronaldo-club.to/api/docs",
        )

        ok = await svc.save_api_config(new_cfg, source=ConfigSource.ADMIN_PANEL, user_id="seed_script")
        if ok:
            print("Seeded api1_external_cart from external_cart ✔")
            return 0
        else:
            print("Failed to save api1_external_cart configuration")
            return 2
    finally:
        await close_database()


def main() -> None:
    try:
        rc = asyncio.run(seed())
        sys.exit(rc)
    except KeyboardInterrupt:
        sys.exit(130)


if __name__ == "__main__":
    main()

