#!/usr/bin/env python3
"""
403 Forbidden Error Diagnostic <PERSON>

This script helps diagnose authentication and authorization issues
causing 403 Forbidden errors in the card service and external API integration.
"""

import asyncio
import logging
import json
import sys
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from config.logging_config import setup_api_logging
from services.card_service import CardService
from services.external_api_service import ExternalAPIService, get_external_api_service
from services.api_config_validation import get_validation_service, ValidationLevel
from services.api_config_service import get_api_config_service
from utils.api_logging import get_api_logger, LogLevel

# Setup logging
api_loggers = setup_api_logging(
    log_level="DEBUG",
    enable_console=True,
    enable_structured=True
)

logger = logging.getLogger(__name__)
diagnostic_logger = get_api_logger("diagnostic", LogLevel.DEBUG)


class APIAuthDiagnostic:
    """Comprehensive API authentication diagnostic tool"""
    
    def __init__(self):
        self.results = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "tests": [],
            "summary": {},
            "recommendations": []
        }
    
    async def run_full_diagnostic(self) -> Dict[str, Any]:
        """Run complete diagnostic suite"""
        logger.info("🔍 Starting comprehensive API authentication diagnostic")
        
        # Test 1: API Configuration
        await self._test_api_configuration()
        
        # Test 2: Card Service Authentication
        await self._test_card_service_auth()
        
        # Test 3: External API Service Authentication
        await self._test_external_api_auth()
        
        # Test 4: Token Validation
        await self._test_token_validation()
        
        # Test 5: Cookie Analysis
        await self._test_cookie_analysis()
        
        # Test 6: Header Analysis
        await self._test_header_analysis()
        
        # Test 7: Endpoint Accessibility
        await self._test_endpoint_accessibility()
        
        # Generate summary and recommendations
        self._generate_summary()
        self._generate_recommendations()
        
        return self.results
    
    async def _test_api_configuration(self):
        """Test API configuration loading and validation"""
        test_result = {
            "test_name": "API Configuration",
            "status": "unknown",
            "details": {},
            "errors": []
        }
        
        try:
            logger.info("Testing API configuration...")
            
            validation_service = get_validation_service()
            api_config_service = get_api_config_service()
            config = await api_config_service.get_api_config("external_cart")
            
            if config:
                test_result["status"] = "success"
                test_result["details"] = {
                    "config_found": True,
                    "base_url": config.base_url,
                    "enabled": config.enabled,
                    "source": config.source.value,
                    "has_credentials": bool(config.credentials),
                    "has_login_token": bool(config.credentials and config.credentials.login_token),
                    "has_cookies": bool(config.credentials and config.credentials.session_cookies),
                    "has_headers": bool(config.credentials and config.credentials.headers),
                    "endpoints_count": len(config.endpoints) if config.endpoints else 0
                }
                
                # Validate credentials
                if config.credentials:
                    if config.credentials.login_token:
                        token_length = len(config.credentials.login_token)
                        test_result["details"]["login_token_length"] = token_length
                        test_result["details"]["login_token_format"] = "JWT" if "." in config.credentials.login_token else "opaque"
                    
                    if config.credentials.session_cookies:
                        test_result["details"]["cookie_names"] = list(config.credentials.session_cookies.keys())
                        test_result["details"]["cookie_count"] = len(config.credentials.session_cookies)
                
                # Run comprehensive validation from APIConfigValidationService
                validation_results = await validation_service.validate_configuration(config)
                test_result["details"]["validation_checks"] = [
                    {"level": r.level.value, "field": r.field, "message": r.message, "suggestion": r.suggestion}
                    for r in validation_results
                ]
                
                if any(r.level == ValidationLevel.ERROR for r in validation_results):
                    test_result["status"] = "error"
                    test_result["errors"].append("API configuration has critical validation errors.")
                elif any(r.level == ValidationLevel.WARNING for r in validation_results):
                    test_result["status"] = "warning"

            else:
                test_result["status"] = "warning"
                test_result["details"]["config_found"] = False
                test_result["errors"].append("No API configuration found for 'external_cart'")
                
        except Exception as e:
            test_result["status"] = "error"
            test_result["errors"].append(f"Configuration test failed: {str(e)}")
        
        self.results["tests"].append(test_result)
    
    async def _test_card_service_auth(self):
        """Test card service authentication"""
        test_result = {
            "test_name": "Card Service Authentication",
            "status": "unknown",
            "details": {},
            "errors": []
        }
        
        try:
            logger.info("Testing card service authentication...")
            
            card_service = CardService()
            async with CardService() as card_service: # Use async with for proper resource management
                # Test configuration loading
                await card_service._load_api_config()
                
                test_result["details"] = {
                    "config_loaded": card_service._config_loaded,
                    "has_api_config": card_service._api_config is not None,
                    "base_url": card_service.base_url,
                    "headers_count": len(card_service.headers),
                    "cookies_count": len(card_service.cookies)
                }
                
                # Test actual API call with diagnostic user ID
                context = diagnostic_logger.create_context(
                    user_id="diagnostic_test",
                    operation="test_fetch_cards"
                )
                
                result = await card_service.fetch_cards(
                    page=1, 
                    limit=1, 
                    user_id="diagnostic_test"
                )
                
                test_result["details"]["api_call_result"] = {
                    "success": result.get("success", False),
                    "error": result.get("error"),
                    "data_count": len(result.get("data", [])),
                    "total_count": result.get("totalCount", 0)
                }
                
                if result.get("success"):
                    test_result["status"] = "success"
                elif "403" in str(result.get("error", "")):
                    test_result["status"] = "error"
                    test_result["errors"].append("403 Forbidden error detected in card service")
                else:
                    test_result["status"] = "warning"
                    test_result["errors"].append(f"API call failed: {result.get('error')}")
            
            # Test configuration loading
            await card_service._load_api_config()
            
            test_result["details"] = {
                "config_loaded": card_service._config_loaded,
                "has_api_config": card_service._api_config is not None,
                "base_url": card_service.base_url,
                "headers_count": len(card_service.headers),
                "cookies_count": len(card_service.cookies)
            }
            
            # Test actual API call with diagnostic user ID
            context = diagnostic_logger.create_context(
                user_id="diagnostic_test",
                operation="test_fetch_cards"
            )
            
            result = await card_service.fetch_cards(
                page=1, 
                limit=1, 
                user_id="diagnostic_test"
            )
            
            test_result["details"]["api_call_result"] = {
                "success": result.get("success", False),
                "error": result.get("error"),
                "data_count": len(result.get("data", [])),
                "total_count": result.get("totalCount", 0)
            }
            
            if result.get("success"):
                test_result["status"] = "success"
            elif "403" in str(result.get("error", "")):
                test_result["status"] = "error"
                test_result["errors"].append("403 Forbidden error detected in card service")
            else:
                test_result["status"] = "warning"
                test_result["errors"].append(f"API call failed: {result.get('error')}")
            
            await card_service.close()
            
        except Exception as e:
            test_result["status"] = "error"
            test_result["errors"].append(f"Card service test failed: {str(e)}")
        
        self.results["tests"].append(test_result)
    
    async def _test_external_api_auth(self):
        """Test external API service authentication"""
        test_result = {
            "test_name": "External API Service Authentication",
            "status": "unknown",
            "details": {},
            "errors": []
        }
        
        try:
            logger.info("Testing external API service authentication...")
            
            external_api_service = get_external_api_service()
            
            # Test user info endpoint (authentication test)
            result = await external_api_service.get_user_info(user_id="diagnostic_test")
            
            test_result["details"] = {
                "api_call_success": result.success,
                "status_code": result.status_code,
                "operation": result.operation.value,
                "execution_time": result.execution_time,
                "has_data": bool(result.data),
                "error": result.error
            }
            
            if result.success:
                test_result["status"] = "success"
            elif result.status_code == 403:
                test_result["status"] = "error"
                test_result["errors"].append("403 Forbidden error detected in external API service")
            else:
                test_result["status"] = "warning"
                test_result["errors"].append(f"External API call failed: {result.error}")
            
        except Exception as e:
            test_result["status"] = "error"
            test_result["errors"].append(f"External API service test failed: {str(e)}")
        
        self.results["tests"].append(test_result)
    
    async def _test_token_validation(self):
        """Test token validation and format"""
        test_result = {
            "test_name": "Token Validation",
            "status": "unknown",
            "details": {},
            "errors": []
        }
        
        try:
            logger.info("Testing token validation...")
            
            api_config_service = get_api_config_service()
            config = await api_config_service.get_api_config("external_cart")
            
            if config and config.credentials and config.credentials.login_token:
                token = config.credentials.login_token
                
                test_result["details"] = {
                    "token_present": True,
                    "token_length": len(token),
                    "token_format": "JWT" if token.count(".") == 2 else "opaque",
                    "starts_with_bearer": token.lower().startswith("bearer "),
                    "has_special_chars": any(c in token for c in "!@#$%^&*()"),
                }
                
                # Basic JWT validation if it looks like a JWT
                if token.count(".") == 2:
                    try:
                        import base64
                        parts = token.split(".")
                        import base64 # Import locally to avoid unnecessary global import if not needed
                        header = json.loads(base64.b64decode(parts[0] + "=="))
                        payload = json.loads(base64.b64decode(parts[1] + "=="))
                        
                        test_result["details"]["jwt_header"] = header
                        test_result["details"]["jwt_payload_keys"] = list(payload.keys())
                        test_result["details"]["jwt_expires"] = payload.get("exp")
                        
                        # Check if token is expired
                        if payload.get("exp"):
                            import time
                            is_expired = payload["exp"] < time.time()
                            test_result["details"]["jwt_expired"] = is_expired
                            if is_expired:
                                test_result["status"] = "error" # Change status to error if token is expired
                                test_result["errors"].append("JWT token appears to be expired")
                        
                        # More robust base64url decode for JWT parts
                        try:
                            # JWTs use base64url encoding, which is slightly different from standard base64
                            # base64.urlsafe_b64decode handles padding automatically
                            json.loads(base64.urlsafe_b64decode(parts[0] + "==").decode('utf-8'))
                            json.loads(base64.urlsafe_b64decode(parts[1] + "==").decode('utf-8'))
                            test_result["details"]["jwt_base64_decode_success"] = True
                        except Exception as decode_error:
                            test_result["details"]["jwt_base64_decode_error"] = str(decode_error)
                        
                    except Exception as jwt_error:
                        test_result["details"]["jwt_parse_error"] = str(jwt_error)
                
                test_result["status"] = "success"
            else:
                test_result["status"] = "warning"
                test_result["details"]["token_present"] = False
                test_result["errors"].append("No login token found in configuration")
                
        except Exception as e:
            test_result["status"] = "error"
            test_result["errors"].append(f"Token validation failed: {str(e)}")
        
        self.results["tests"].append(test_result)
    
    async def _test_cookie_analysis(self):
        """Analyze session cookies"""
        test_result = {
            "test_name": "Cookie Analysis",
            "status": "unknown",
            "details": {},
            "errors": []
        }
        
        try:
            logger.info("Analyzing session cookies...")
            
            api_config_service = get_api_config_service()
            config = await api_config_service.get_api_config("external_cart")
            
            if config and config.credentials and config.credentials.session_cookies:
                cookies = config.credentials.session_cookies
                
                test_result["details"] = {
                    "cookies_present": True,
                    "cookie_count": len(cookies),
                    "cookie_names": list(cookies.keys()),
                    "expected_cookies": ["__ddg1_", "__ddg8_", "__ddg9_", "__ddg10_", "testcookie"],
                }
                
                # Check for expected cookies from demo
                expected_cookies = ["__ddg1_", "__ddg8_", "__ddg9_", "__ddg10_", "testcookie"]
                missing_cookies = [c for c in expected_cookies if c not in cookies]
                extra_cookies = [c for c in cookies if c not in expected_cookies]
                
                test_result["details"]["missing_cookies"] = missing_cookies
                test_result["details"]["extra_cookies"] = extra_cookies
                
                if missing_cookies:
                    test_result["errors"].append(f"Missing expected cookies: {missing_cookies}")
                
                test_result["status"] = "success" if not missing_cookies else "warning"
            else:
                test_result["status"] = "warning"
                test_result["details"]["cookies_present"] = False
                test_result["errors"].append("No session cookies found in configuration")
                
        except Exception as e:
            test_result["status"] = "error"
            test_result["errors"].append(f"Cookie analysis failed: {str(e)}")
        
        self.results["tests"].append(test_result)
    
    async def _test_header_analysis(self):
        """Analyze request headers"""
        test_result = {
            "test_name": "Header Analysis",
            "status": "success",
            "details": {},
            "errors": []
        }
        
        try:
            logger.info("Analyzing request headers...")
            
            card_service = CardService()
            await card_service._load_api_config()
            
            headers = card_service.headers
            
            test_result["details"] = {
                "headers_count": len(headers),
                "has_user_agent": "User-Agent" in headers,
                "has_accept": "Accept" in headers,
                "has_origin": "Origin" in headers,
                "has_referer": "Referer" in headers,
                "header_names": list(headers.keys())
            }
            
            # Check for required headers
            required_headers = ["User-Agent", "Accept", "Origin", "Referer"]
            missing_headers = [h for h in required_headers if h not in headers]
            
            if missing_headers:
                test_result["status"] = "warning"
                test_result["errors"].append(f"Missing recommended headers: {missing_headers}")
            
        except Exception as e:
            test_result["status"] = "error"
            test_result["errors"].append(f"Header analysis failed: {str(e)}")
        
        self.results["tests"].append(test_result)
    
    async def _test_endpoint_accessibility(self):
        """Test endpoint accessibility"""
        test_result = {
            "test_name": "Endpoint Accessibility",
            "status": "unknown",
            "details": {},
            "errors": []
        }
        
        try:
            logger.info("Testing endpoint accessibility...")
            
            # This would require actual HTTP requests to test endpoints
            # For now, we'll just validate the configuration
            
            api_config_service = get_api_config_service()
            config = await api_config_service.get_api_config("external_cart")
            
            if config:
                validation_service = get_validation_service()
                connection_test_result = await validation_service.test_api_connection(config)
                
                test_result["details"] = {
                    "base_url": config.base_url,
                    "base_url_accessible": "https://" in config.base_url,  # Basic check
                    "endpoints_configured": len(config.endpoints) if config.endpoints else 0
                    "connection_test_success": connection_test_result.success,
                    "connection_test_status_code": connection_test_result.status_code,
                    "connection_test_error": connection_test_result.error_message,
                    "connection_test_suggestions": connection_test_result.suggestions,
                    "endpoint_test_results": connection_test_result.endpoint_results,
                }
                test_result["status"] = "success"
                
                if connection_test_result.success:
                    test_result["status"] = "success"
                else:
                    test_result["status"] = "error"
                    test_result["errors"].append(f"Endpoint accessibility test failed: {connection_test_result.error_message}")
                    test_result["errors"].extend(connection_test_result.suggestions)
            else:
                test_result["status"] = "error"
                test_result["errors"].append("No API configuration available for endpoint testing")
                
        except Exception as e:
            test_result["status"] = "error"
            test_result["errors"].append(f"Endpoint accessibility test failed: {str(e)}")
        
        self.results["tests"].append(test_result)
    
    def _generate_summary(self):
        """Generate diagnostic summary"""
        total_tests = len(self.results["tests"])
        success_count = sum(1 for test in self.results["tests"] if test["status"] == "success")
        warning_count = sum(1 for test in self.results["tests"] if test["status"] == "warning")
        error_count = sum(1 for test in self.results["tests"] if test["status"] == "error")
        
        self.results["summary"] = {
            "total_tests": total_tests,
            "success_count": success_count,
            "warning_count": warning_count,
            "error_count": error_count,
            "success_rate": (success_count / total_tests) * 100 if total_tests > 0 else 0,
            "overall_status": "healthy" if error_count == 0 else "issues_detected"
        }
    
    def _generate_recommendations(self):
        """Generate recommendations based on test results"""
        recommendations = []
        
        for test in self.results["tests"]:
            if test["status"] == "error":
                if "403 Forbidden" in str(test["errors"]):
                    recommendations.extend([
                        "🔑 Check authentication tokens - they may be expired or invalid",
                        "🍪 Verify session cookies are properly configured",
                        "🔗 Ensure API endpoints are correct and accessible",
                        "👤 Confirm user permissions for the requested operations"
                    ])
                elif "Configuration" in test["test_name"]:
                elif "Configuration" in test["test_name"] and "critical validation errors" in str(test["errors"]):
                    recommendations.extend([
                        "⚙️ Configure the 'external_cart' API in the admin panel",
                        "🔧 Use the External Cart API template for proper setup",
                        "📝 Review detailed validation checks in the diagnostic report for specific errors",
                        "🌐 Ensure base URL and endpoint URLs are correctly formatted and accessible",
                        "📝 Verify all required credentials are provided"
                    ])
            elif test["status"] == "warning":
                if "Token" in test["test_name"]:
                    recommendations.append("🔄 Consider refreshing authentication tokens")
                elif "Cookie" in test["test_name"]:
                    recommendations.append("🍪 Update session cookies from a fresh browser session")
                elif "Configuration" in test["test_name"] and "validation checks" in str(test["details"]):
                    recommendations.append("⚠️ Review API configuration warnings for potential improvements")
        
        # Remove duplicates
        self.results["recommendations"] = list(set(recommendations))


async def main():
    """Run the diagnostic script"""
    print("🔍 API Authentication Diagnostic Tool")
    print("=" * 50)
    
    diagnostic = APIAuthDiagnostic()
    results = await diagnostic.run_full_diagnostic()
    
    # Print results
    print(f"\n📊 Diagnostic Summary:")
    print(f"Total Tests: {results['summary']['total_tests']}")
    print(f"Success: {results['summary']['success_count']}")
    print(f"Warnings: {results['summary']['warning_count']}")
    print(f"Errors: {results['summary']['error_count']}")
    print(f"Success Rate: {results['summary']['success_rate']:.1f}%")
    print(f"Overall Status: {results['summary']['overall_status'].upper()}")
    
    print(f"\n📋 Test Results:")
    for test in results["tests"]:
        status_icon = {"success": "✅", "warning": "⚠️", "error": "❌", "unknown": "❓"}[test["status"]]
        print(f"{status_icon} {test['test_name']}: {test['status'].upper()}")
        if test["errors"]:
            for error in test["errors"]:
                print(f"   └─ {error}")
    
    if results["recommendations"]:
        print(f"\n💡 Recommendations:")
        for rec in results["recommendations"]:
            print(f"   {rec}")
    
    # Save detailed results
    results_file = Path("logs") / f"api_diagnostic_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    results_file.parent.mkdir(exist_ok=True)
    
    with open(results_file, "w") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 Detailed results saved to: {results_file}")
    
    return results["summary"]["error_count"] == 0


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Diagnostic interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Diagnostic failed: {e}")
        sys.exit(1)
