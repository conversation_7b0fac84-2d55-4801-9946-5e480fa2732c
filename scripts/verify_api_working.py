#!/usr/bin/env python3
"""
Simple verification that the API integration is working
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from services.card_service import CardService

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)


async def verify_api_working():
    """Verify that the API integration is working"""
    logger.info("🔍 Verifying API Integration Status")
    logger.info("=" * 50)
    
    try:
        # Initialize card service (no database needed for this test)
        card_service = CardService()
        
        # Test card fetching
        logger.info("Testing card fetching...")
        result = await card_service.fetch_cards(page=1, limit=3, user_id="verification_test")
        
        if result.get("success"):
            cards = result.get('data', [])
            total = result.get('totalCount', 0)
            
            logger.info("✅ API Integration is WORKING!")
            logger.info(f"   Successfully fetched {len(cards)} cards")
            logger.info(f"   Total cards available: {total:,}")
            
            if cards:
                sample_card = cards[0]
                logger.info(f"   Sample card: {sample_card.get('bin')} - {sample_card.get('bank')}")
                logger.info(f"   Location: {sample_card.get('city')}, {sample_card.get('state')}")
                logger.info(f"   Price: ${sample_card.get('price')}")
            
            logger.info("\n🎉 JWT Token Authentication: SUCCESS")
            logger.info("🎉 Card Service: FUNCTIONAL")
            logger.info("🎉 API Integration: COMPLETE")
            
            await card_service.close()
            return True
        else:
            logger.error(f"❌ API test failed: {result.get('error')}")
            await card_service.close()
            return False
            
    except Exception as e:
        logger.error(f"❌ Verification failed: {e}")
        return False


async def main():
    """Main verification function"""
    try:
        success = await verify_api_working()
        if success:
            logger.info("\n✅ VERIFICATION COMPLETE: API integration is working perfectly!")
            logger.info("💡 The bot is ready to use with the external API")
        else:
            logger.error("\n❌ VERIFICATION FAILED: API integration needs attention")
        return success
    except Exception as e:
        logger.error(f"Verification script failed: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
