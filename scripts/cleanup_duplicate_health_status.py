#!/usr/bin/env python3
"""
Cleanup script for duplicate API health status records
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from database.connection import init_database, close_database, get_collection
import logging

logger = logging.getLogger(__name__)

async def cleanup_duplicate_health_status():
    """Remove duplicate API health status records"""
    try:
        await init_database()
        
        health_collection = get_collection("api_health_status")
        
        print("🔍 Checking for duplicate API health status records...")
        
        # Find all records
        cursor = health_collection.find({})
        all_records = await cursor.to_list(None)
        
        print(f"Found {len(all_records)} total health status records")
        
        # Group by api_config_id to find duplicates
        config_groups = {}
        for record in all_records:
            config_id = record.get("api_config_id")
            if config_id not in config_groups:
                config_groups[config_id] = []
            config_groups[config_id].append(record)
        
        # Find and remove duplicates
        duplicates_found = 0
        records_removed = 0
        
        for config_id, records in config_groups.items():
            if len(records) > 1:
                duplicates_found += 1
                print(f"⚠️  Found {len(records)} duplicate records for API config: {config_id}")
                
                # Keep the most recent record (by created_at or _id)
                records.sort(key=lambda x: x.get("created_at", x.get("_id")), reverse=True)
                keep_record = records[0]
                remove_records = records[1:]
                
                print(f"   Keeping record: {keep_record['_id']}")
                
                # Remove duplicate records
                for record in remove_records:
                    result = await health_collection.delete_one({"_id": record["_id"]})
                    if result.deleted_count > 0:
                        records_removed += 1
                        print(f"   ✅ Removed duplicate record: {record['_id']}")
                    else:
                        print(f"   ❌ Failed to remove record: {record['_id']}")
        
        if duplicates_found == 0:
            print("✅ No duplicate records found!")
        else:
            print(f"\n📊 Cleanup Summary:")
            print(f"   - API configs with duplicates: {duplicates_found}")
            print(f"   - Duplicate records removed: {records_removed}")
            print("✅ Cleanup completed!")
        
        # Verify the unique index exists
        try:
            indexes = await health_collection.list_indexes().to_list(None)
            has_unique_index = any(
                idx.get("key", {}).get("api_config_id") == 1 and idx.get("unique", False)
                for idx in indexes
            )
            
            if has_unique_index:
                print("✅ Unique index on api_config_id exists")
            else:
                print("⚠️  Creating unique index on api_config_id...")
                await health_collection.create_index(
                    "api_config_id", 
                    unique=True, 
                    background=True
                )
                print("✅ Unique index created")
                
        except Exception as e:
            print(f"⚠️  Index check/creation failed: {e}")
        
    except Exception as e:
        print(f"❌ Cleanup failed: {e}")
        logger.error(f"Cleanup failed: {e}")
    
    finally:
        await close_database()

async def main():
    """Main cleanup function"""
    logging.basicConfig(level=logging.INFO)
    
    print("🧹 API Health Status Cleanup Tool")
    print("=" * 40)
    
    await cleanup_duplicate_health_status()
    
    print("\n🎉 Cleanup process completed!")

if __name__ == "__main__":
    asyncio.run(main())
