#!/usr/bin/env python3
"""
Comprehensive security testing script for Demo Wallet Bot v2
Tests all security improvements and validates production readiness
"""

import asyncio
import sys
import time
import logging
from typing import List, Dict, Any
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from utils.security import (
    check_rate_limit_security,
    hash_password,
    verify_password,
    sanitize_user_input,
)
from utils.validation import (
    validate_amount,
    validate_telegram_id,
    sanitize_text_input,
    ValidationError,
)
from config.settings import get_settings

logger = logging.getLogger(__name__)


class SecurityTestSuite:
    """Comprehensive security test suite"""

    def __init__(self):
        self.settings = get_settings()
        self.test_results: List[Dict[str, Any]] = []
        self.passed_tests = 0
        self.failed_tests = 0

    def log_test_result(self, test_name: str, passed: bool, details: str = ""):
        """Log test result"""
        result = {
            "test_name": test_name,
            "passed": passed,
            "details": details,
            "timestamp": time.time()
        }
        self.test_results.append(result)
        
        if passed:
            self.passed_tests += 1
            logger.info(f"✅ {test_name}: PASSED")
        else:
            self.failed_tests += 1
            logger.error(f"❌ {test_name}: FAILED - {details}")

    def test_password_security(self):
        """Test password hashing and verification"""
        test_name = "Password Security"
        try:
            # Test password hashing
            password = "test_password_123!@#"
            hashed, salt = hash_password(password)
            
            # Verify hash properties
            assert isinstance(hashed, str), "Hash should be string"
            assert len(hashed) == 64, "Hash should be 64 characters (SHA-256 hex)"
            assert isinstance(salt, bytes), "Salt should be bytes"
            assert len(salt) == 32, "Salt should be 32 bytes"
            
            # Test verification
            assert verify_password(password, hashed, salt), "Password verification should succeed"
            assert not verify_password("wrong_password", hashed, salt), "Wrong password should fail"
            
            # Test different passwords produce different hashes
            hashed2, salt2 = hash_password(password)
            assert hashed != hashed2, "Different salts should produce different hashes"
            
            self.log_test_result(test_name, True)
            
        except Exception as e:
            self.log_test_result(test_name, False, str(e))

    def test_rate_limiting(self):
        """Test rate limiting functionality"""
        test_name = "Rate Limiting"
        try:
            user_id = 999999
            action = "test_security_action"
            
            # Clear any existing rate limit data
            from utils.security import _rate_limit_data
            _rate_limit_data.clear()
            
            # Test normal operation
            assert check_rate_limit_security(user_id, action, max_attempts=3, window_seconds=60), "First request should pass"
            assert check_rate_limit_security(user_id, action, max_attempts=3, window_seconds=60), "Second request should pass"
            assert check_rate_limit_security(user_id, action, max_attempts=3, window_seconds=60), "Third request should pass"
            
            # Test rate limiting
            assert not check_rate_limit_security(user_id, action, max_attempts=3, window_seconds=60), "Fourth request should be blocked"
            
            # Test different user is not affected
            assert check_rate_limit_security(888888, action, max_attempts=3, window_seconds=60), "Different user should not be affected"
            
            self.log_test_result(test_name, True)
            
        except Exception as e:
            self.log_test_result(test_name, False, str(e))

    def test_input_validation(self):
        """Test input validation functions"""
        test_name = "Input Validation"
        try:
            # Test amount validation
            assert validate_amount("10.50") == 10.50, "Valid amount should pass"
            assert validate_amount(100) == 100.0, "Integer amount should pass"
            
            # Test invalid amounts
            try:
                validate_amount("-10")
                assert False, "Negative amount should fail"
            except ValidationError:
                pass
            
            try:
                validate_amount("abc")
                assert False, "Non-numeric amount should fail"
            except ValidationError:
                pass
            
            # Test Telegram ID validation
            assert validate_telegram_id(123456789) == 123456789, "Valid Telegram ID should pass"
            
            try:
                validate_telegram_id(-1)
                assert False, "Negative Telegram ID should fail"
            except ValidationError:
                pass
            
            self.log_test_result(test_name, True)
            
        except Exception as e:
            self.log_test_result(test_name, False, str(e))

    def test_input_sanitization(self):
        """Test input sanitization"""
        test_name = "Input Sanitization"
        try:
            # Test normal input
            clean = sanitize_text_input("Hello World")
            assert clean == "Hello World", "Normal text should pass unchanged"
            
            # Test HTML removal
            html_input = "<script>alert('xss')</script>Hello"
            clean = sanitize_text_input(html_input)
            assert "<script>" not in clean, "HTML tags should be removed"
            assert "Hello" in clean, "Safe content should remain"
            
            # Test JavaScript removal
            js_input = "javascript:alert('xss')"
            clean = sanitize_text_input(js_input)
            assert "javascript:" not in clean, "JavaScript should be removed"
            
            # Test length validation
            try:
                sanitize_text_input("x" * 300, max_length=100)
                assert False, "Long input should fail"
            except ValidationError:
                pass
            
            self.log_test_result(test_name, True)
            
        except Exception as e:
            self.log_test_result(test_name, False, str(e))

    def test_configuration_security(self):
        """Test security configuration"""
        test_name = "Configuration Security"
        try:
            settings = get_settings()
            
            # Test required security settings exist
            assert hasattr(settings, 'MONGODB_AUTH_SOURCE'), "MongoDB auth source should be configured"
            assert hasattr(settings, 'MONGODB_AUTH_MECHANISM'), "MongoDB auth mechanism should be configured"
            assert settings.MONGODB_AUTH_MECHANISM == 'SCRAM-SHA-256', "Should use secure auth mechanism"
            
            # Test rate limiting is configured
            assert settings.PURCHASES_PER_MINUTE > 0, "Purchase rate limit should be set"
            assert settings.MESSAGES_PER_MINUTE > 0, "Message rate limit should be set"
            
            # Test compliance settings
            assert len(settings.sanctioned_countries_set) > 0, "Sanctioned countries should be configured"
            assert settings.AML_HOURLY_LIMIT > 0, "AML limits should be set"
            
            self.log_test_result(test_name, True)
            
        except Exception as e:
            self.log_test_result(test_name, False, str(e))

    def test_database_security(self):
        """Test database security configuration"""
        test_name = "Database Security"
        try:
            from database.connection import DatabaseManager
            
            # Test database connection security
            db_manager = DatabaseManager()
            
            # In production, these should be properly configured
            if self.settings.ENVIRONMENT == "production":
                assert self.settings.USE_MONGODB, "Production should use MongoDB"
                assert "ssl=true" in self.settings.MONGODB_URL.lower() or "tls=true" in self.settings.MONGODB_URL.lower(), "Production should use SSL/TLS"
            
            self.log_test_result(test_name, True)
            
        except Exception as e:
            self.log_test_result(test_name, False, str(e))

    def test_logging_security(self):
        """Test security logging functionality"""
        test_name = "Security Logging"
        try:
            from utils.logging import log_security_event, setup_security_logging
            
            # Test security logging setup
            setup_security_logging("logs/test_security.log")
            
            # Test security event logging
            log_security_event(
                event_type="test_event",
                user_id=12345,
                action="security_test",
                result="success",
                details="Test security event",
                level="INFO"
            )
            
            # Verify log file was created
            log_file = Path("logs/test_security.log")
            assert log_file.exists(), "Security log file should be created"
            
            # Clean up test log
            if log_file.exists():
                log_file.unlink()
            
            self.log_test_result(test_name, True)
            
        except Exception as e:
            self.log_test_result(test_name, False, str(e))

    def test_error_handling(self):
        """Test error handling security"""
        test_name = "Error Handling Security"
        try:
            from middleware.error_handling import ErrorHandlingMiddleware
            
            middleware = ErrorHandlingMiddleware()
            
            # Test error message sanitization
            test_errors = [
                ValueError("Invalid input"),
                ConnectionError("Database connection failed"),
                Exception("rate limit exceeded"),
                Exception("security validation failed"),
            ]
            
            for error in test_errors:
                message = middleware._get_error_message(error)
                assert isinstance(message, str), "Error message should be string"
                assert len(message) > 0, "Error message should not be empty"
                assert "❌" in message or "⏳" in message or "🔒" in message, "Error message should have appropriate emoji"
            
            self.log_test_result(test_name, True)
            
        except Exception as e:
            self.log_test_result(test_name, False, str(e))

    async def run_all_tests(self):
        """Run all security tests"""
        logger.info("🔒 Starting comprehensive security test suite...")
        
        # Run all tests
        self.test_password_security()
        self.test_rate_limiting()
        self.test_input_validation()
        self.test_input_sanitization()
        self.test_configuration_security()
        self.test_database_security()
        self.test_logging_security()
        self.test_error_handling()
        
        # Generate report
        self.generate_report()

    def generate_report(self):
        """Generate security test report"""
        total_tests = self.passed_tests + self.failed_tests
        success_rate = (self.passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print("\n" + "="*60)
        print("🔒 SECURITY TEST REPORT")
        print("="*60)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {self.passed_tests}")
        print(f"Failed: {self.failed_tests}")
        print(f"Success Rate: {success_rate:.1f}%")
        print("="*60)
        
        if self.failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for result in self.test_results:
                if not result["passed"]:
                    print(f"  - {result['test_name']}: {result['details']}")
        
        if success_rate == 100:
            print("\n✅ All security tests passed! System is ready for production.")
        else:
            print(f"\n⚠️  {self.failed_tests} security tests failed. Address issues before production deployment.")
        
        print("="*60)


async def main():
    """Main test runner"""
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create logs directory
    Path("logs").mkdir(exist_ok=True)
    
    # Run security tests
    test_suite = SecurityTestSuite()
    await test_suite.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if test_suite.failed_tests == 0 else 1)


if __name__ == "__main__":
    asyncio.run(main())
