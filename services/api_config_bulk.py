"""
API Configuration Bulk Operations Service
Handles import/export, bulk editing, and batch operations for API configurations
"""

from __future__ import annotations

import json
import yaml
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timezone
from dataclasses import asdict
import asyncio

from services.api_config_service import (
    get_api_config_service, 
    APIConfiguration, 
    APIEndpoint, 
    APICredentials,
    ConfigSource
)

logger = logging.getLogger(__name__)


class BulkOperationResult:
    """Result of a bulk operation"""
    
    def __init__(self):
        self.success_count: int = 0
        self.error_count: int = 0
        self.errors: List[Dict[str, str]] = []
        self.warnings: List[str] = []
        self.processed_items: List[str] = []
    
    def add_success(self, item_name: str):
        """Add a successful operation"""
        self.success_count += 1
        self.processed_items.append(item_name)
    
    def add_error(self, item_name: str, error: str):
        """Add an error"""
        self.error_count += 1
        self.errors.append({"item": item_name, "error": error})
    
    def add_warning(self, warning: str):
        """Add a warning"""
        self.warnings.append(warning)
    
    @property
    def total_count(self) -> int:
        return self.success_count + self.error_count
    
    @property
    def is_success(self) -> bool:
        return self.error_count == 0
    
    def get_summary(self) -> str:
        """Get a summary of the operation"""
        summary = f"✅ {self.success_count} successful"
        if self.error_count > 0:
            summary += f", ❌ {self.error_count} failed"
        if self.warnings:
            summary += f", ⚠️ {len(self.warnings)} warnings"
        return summary


class APIConfigBulkService:
    """Service for bulk operations on API configurations"""
    
    def __init__(self):
        self.api_service = get_api_config_service()
    
    async def export_configurations(
        self, 
        service_names: Optional[List[str]] = None,
        format: str = "json",
        include_credentials: bool = False
    ) -> str:
        """Export API configurations to JSON or YAML format"""
        try:
            # Get configurations to export
            if service_names:
                configs = []
                for name in service_names:
                    config = await self.api_service.get_api_config(name)
                    if config:
                        configs.append(config)
            else:
                all_configs = await self.api_service.get_all_configurations()
                configs = list(all_configs.values())
            
            # Convert to exportable format
            export_data = {
                "version": "2.0",
                "exported_at": datetime.now(timezone.utc).isoformat(),
                "configurations": []
            }
            
            for config in configs:
                config_dict = asdict(config)
                
                # Remove sensitive data if not requested
                if not include_credentials:
                    config_dict["credentials"]["login_token"] = "***REDACTED***"
                    config_dict["credentials"]["session_cookies"] = {}
                
                # Clean up datetime objects
                if isinstance(config_dict.get("last_updated"), datetime):
                    config_dict["last_updated"] = config_dict["last_updated"].isoformat()
                
                export_data["configurations"].append(config_dict)
            
            # Format output
            if format.lower() == "yaml":
                return yaml.dump(export_data, default_flow_style=False, indent=2)
            else:
                return json.dumps(export_data, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"Export failed: {e}")
            raise
    
    async def import_configurations(
        self, 
        data: str, 
        format: str = "json",
        overwrite_existing: bool = False,
        user_id: str = ""
    ) -> BulkOperationResult:
        """Import API configurations from JSON or YAML data"""
        result = BulkOperationResult()
        
        try:
            # Parse input data
            if format.lower() == "yaml":
                import_data = yaml.safe_load(data)
            else:
                import_data = json.loads(data)
            
            if not isinstance(import_data, dict) or "configurations" not in import_data:
                result.add_error("import", "Invalid import format - missing 'configurations' key")
                return result
            
            configurations = import_data["configurations"]
            if not isinstance(configurations, list):
                result.add_error("import", "Invalid import format - 'configurations' must be a list")
                return result
            
            # Process each configuration
            for config_data in configurations:
                try:
                    service_name = config_data.get("service_name")
                    if not service_name:
                        result.add_error("unknown", "Missing service_name in configuration")
                        continue
                    
                    # Check if configuration already exists
                    existing_config = await self.api_service.get_api_config(service_name)
                    if existing_config and not overwrite_existing:
                        result.add_warning(f"Skipped existing configuration: {service_name}")
                        continue
                    
                    # Build configuration object
                    config = self._build_config_from_dict(config_data)
                    if not config:
                        result.add_error(service_name, "Failed to build configuration from data")
                        continue
                    
                    # Save configuration
                    success = await self.api_service.save_api_config(
                        config, 
                        ConfigSource.ADMIN_PANEL, 
                        user_id
                    )
                    
                    if success:
                        result.add_success(service_name)
                    else:
                        result.add_error(service_name, "Failed to save configuration")
                        
                except Exception as e:
                    result.add_error(service_name or "unknown", str(e))
            
            return result
            
        except Exception as e:
            result.add_error("import", f"Import failed: {str(e)}")
            return result
    
    async def bulk_update_configurations(
        self,
        updates: Dict[str, Dict[str, Any]],
        user_id: str = ""
    ) -> BulkOperationResult:
        """Apply bulk updates to multiple configurations"""
        result = BulkOperationResult()
        
        for service_name, update_data in updates.items():
            try:
                # Get existing configuration
                config = await self.api_service.get_api_config(service_name)
                if not config:
                    result.add_error(service_name, "Configuration not found")
                    continue
                
                # Apply updates
                for field, value in update_data.items():
                    if hasattr(config, field):
                        setattr(config, field, value)
                    else:
                        result.add_warning(f"Unknown field '{field}' for {service_name}")
                
                # Save updated configuration
                success = await self.api_service.save_api_config(
                    config,
                    ConfigSource.ADMIN_PANEL,
                    user_id
                )
                
                if success:
                    result.add_success(service_name)
                else:
                    result.add_error(service_name, "Failed to save updated configuration")
                    
            except Exception as e:
                result.add_error(service_name, str(e))
        
        return result
    
    async def bulk_enable_disable(
        self,
        service_names: List[str],
        enabled: bool,
        user_id: str = ""
    ) -> BulkOperationResult:
        """Bulk enable or disable configurations"""
        updates = {name: {"enabled": enabled} for name in service_names}
        return await self.bulk_update_configurations(updates, user_id)
    
    async def bulk_delete_configurations(
        self,
        service_names: List[str],
        user_id: str = ""
    ) -> BulkOperationResult:
        """Bulk delete configurations"""
        result = BulkOperationResult()
        
        for service_name in service_names:
            try:
                success = await self.api_service.delete_api_config(service_name, user_id)
                if success:
                    result.add_success(service_name)
                else:
                    result.add_error(service_name, "Failed to delete configuration")
            except Exception as e:
                result.add_error(service_name, str(e))
        
        return result
    
    def _build_config_from_dict(self, config_data: Dict[str, Any]) -> Optional[APIConfiguration]:
        """Build APIConfiguration object from dictionary data"""
        try:
            # Build endpoints
            endpoints = {}
            for name, endpoint_data in config_data.get("endpoints", {}).items():
                endpoints[name] = APIEndpoint(
                    name=endpoint_data.get("name", name),
                    url=endpoint_data.get("url", ""),
                    method=endpoint_data.get("method", "GET"),
                    timeout=endpoint_data.get("timeout", 30),
                    retry_count=endpoint_data.get("retry_count", 3),
                    retry_delays=endpoint_data.get("retry_delays", [1, 2, 4])
                )
            
            # Build credentials
            creds_data = config_data.get("credentials", {})
            credentials = APICredentials(
                login_token=creds_data.get("login_token", ""),
                session_cookies=creds_data.get("session_cookies", {}),
                headers=creds_data.get("headers", {})
            )
            
            # Build configuration
            config = APIConfiguration(
                service_name=config_data["service_name"],
                base_url=config_data.get("base_url", ""),
                endpoints=endpoints,
                credentials=credentials,
                enabled=config_data.get("enabled", True),
                source=ConfigSource.ADMIN_PANEL,
                display_name=config_data.get("display_name", ""),
                description=config_data.get("description", ""),
                category=config_data.get("category", "general"),
                tags=config_data.get("tags", []),
                environment=config_data.get("environment", "development"),
                version=config_data.get("version", "1.0"),
                health_check_endpoint=config_data.get("health_check_endpoint", ""),
                documentation_url=config_data.get("documentation_url", "")
            )
            
            return config
            
        except Exception as e:
            logger.error(f"Failed to build config from dict: {e}")
            return None


# Global instance
_bulk_service = None

def get_bulk_service() -> APIConfigBulkService:
    """Get the global bulk service instance"""
    global _bulk_service
    if _bulk_service is None:
        _bulk_service = APIConfigBulkService()
    return _bulk_service
