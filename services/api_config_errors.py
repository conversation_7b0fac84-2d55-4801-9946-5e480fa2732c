"""
Enhanced Error Handling and Recovery Service for API Configuration Management
Provides detailed error messages, recovery suggestions, and user-friendly error handling
"""

from __future__ import annotations

import logging
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
from dataclasses import dataclass

logger = logging.getLogger(__name__)


class ErrorCategory(str, Enum):
    """Categories of errors for better organization"""
    VALIDATION = "validation"
    CONNECTION = "connection"
    AUTHENTICATION = "authentication"
    CONFIGURATION = "configuration"
    PERMISSION = "permission"
    SYSTEM = "system"
    USER_INPUT = "user_input"


class ErrorSeverity(str, Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ErrorInfo:
    """Detailed error information with recovery suggestions"""
    code: str
    category: ErrorCategory
    severity: ErrorSeverity
    title: str
    message: str
    technical_details: Optional[str] = None
    recovery_suggestions: List[str] = None
    related_docs: Optional[str] = None
    contact_support: bool = False

    def __post_init__(self):
        if self.recovery_suggestions is None:
            self.recovery_suggestions = []


class APIConfigErrorService:
    """Enhanced error handling service for API configuration management"""
    
    def __init__(self):
        self._error_catalog = self._build_error_catalog()
    
    def _build_error_catalog(self) -> Dict[str, ErrorInfo]:
        """Build comprehensive error catalog with recovery suggestions"""
        return {
            # Validation Errors
            "INVALID_SERVICE_NAME": ErrorInfo(
                code="INVALID_SERVICE_NAME",
                category=ErrorCategory.VALIDATION,
                severity=ErrorSeverity.MEDIUM,
                title="Invalid Service Name",
                message="Service name contains invalid characters or format",
                recovery_suggestions=[
                    "Use only lowercase letters, numbers, and underscores",
                    "Start with a letter, not a number",
                    "Keep it descriptive but concise (e.g., 'payment_gateway')",
                    "Avoid spaces and special characters"
                ]
            ),
            
            "INVALID_URL_FORMAT": ErrorInfo(
                code="INVALID_URL_FORMAT",
                category=ErrorCategory.VALIDATION,
                severity=ErrorSeverity.HIGH,
                title="Invalid URL Format",
                message="The provided URL is not in a valid format",
                recovery_suggestions=[
                    "Ensure URL starts with http:// or https://",
                    "Check for typos in the domain name",
                    "Verify the URL is accessible in a browser",
                    "Use https:// for better security when possible"
                ]
            ),
            
            "MISSING_REQUIRED_FIELD": ErrorInfo(
                code="MISSING_REQUIRED_FIELD",
                category=ErrorCategory.VALIDATION,
                severity=ErrorSeverity.HIGH,
                title="Missing Required Information",
                message="One or more required fields are missing",
                recovery_suggestions=[
                    "Check all required fields are filled",
                    "Review the template requirements",
                    "Ensure no fields are left empty",
                    "Use the validation feature to identify missing fields"
                ]
            ),
            
            # Connection Errors
            "CONNECTION_TIMEOUT": ErrorInfo(
                code="CONNECTION_TIMEOUT",
                category=ErrorCategory.CONNECTION,
                severity=ErrorSeverity.MEDIUM,
                title="Connection Timeout",
                message="Unable to connect to the API within the timeout period",
                recovery_suggestions=[
                    "Check if the API server is running and accessible",
                    "Verify your internet connection",
                    "Try increasing the timeout value in settings",
                    "Check if there are any firewall restrictions",
                    "Test the URL in a web browser or API client"
                ]
            ),
            
            "CONNECTION_REFUSED": ErrorInfo(
                code="CONNECTION_REFUSED",
                category=ErrorCategory.CONNECTION,
                severity=ErrorSeverity.HIGH,
                title="Connection Refused",
                message="The API server refused the connection",
                recovery_suggestions=[
                    "Verify the API URL is correct",
                    "Check if the API server is running",
                    "Ensure the port number is correct",
                    "Check for network connectivity issues",
                    "Contact the API provider if the issue persists"
                ]
            ),
            
            "DNS_RESOLUTION_FAILED": ErrorInfo(
                code="DNS_RESOLUTION_FAILED",
                category=ErrorCategory.CONNECTION,
                severity=ErrorSeverity.HIGH,
                title="DNS Resolution Failed",
                message="Unable to resolve the API domain name",
                recovery_suggestions=[
                    "Check the domain name for typos",
                    "Verify the domain exists and is accessible",
                    "Try using a different DNS server",
                    "Check your internet connection",
                    "Wait and try again if it's a temporary DNS issue"
                ]
            ),
            
            # Authentication Errors
            "INVALID_CREDENTIALS": ErrorInfo(
                code="INVALID_CREDENTIALS",
                category=ErrorCategory.AUTHENTICATION,
                severity=ErrorSeverity.HIGH,
                title="Invalid Credentials",
                message="The provided authentication credentials are invalid",
                recovery_suggestions=[
                    "Verify your API key or token is correct",
                    "Check if credentials have expired",
                    "Ensure you're using the right authentication method",
                    "Generate new credentials if needed",
                    "Check the API documentation for authentication requirements"
                ]
            ),
            
            "INSUFFICIENT_PERMISSIONS": ErrorInfo(
                code="INSUFFICIENT_PERMISSIONS",
                category=ErrorCategory.AUTHENTICATION,
                severity=ErrorSeverity.MEDIUM,
                title="Insufficient Permissions",
                message="Your credentials don't have permission for this operation",
                recovery_suggestions=[
                    "Check if your API key has the required scopes",
                    "Contact your API provider to request additional permissions",
                    "Verify you're using the correct account credentials",
                    "Review the API documentation for permission requirements"
                ]
            ),
            
            "TOKEN_EXPIRED": ErrorInfo(
                code="TOKEN_EXPIRED",
                category=ErrorCategory.AUTHENTICATION,
                severity=ErrorSeverity.MEDIUM,
                title="Authentication Token Expired",
                message="Your authentication token has expired",
                recovery_suggestions=[
                    "Generate a new authentication token",
                    "Update the configuration with the new token",
                    "Set up automatic token renewal if supported",
                    "Check token expiration policies with your API provider"
                ]
            ),
            
            # Configuration Errors
            "DUPLICATE_SERVICE_NAME": ErrorInfo(
                code="DUPLICATE_SERVICE_NAME",
                category=ErrorCategory.CONFIGURATION,
                severity=ErrorSeverity.MEDIUM,
                title="Duplicate Service Name",
                message="A configuration with this service name already exists",
                recovery_suggestions=[
                    "Choose a different, unique service name",
                    "Edit the existing configuration instead",
                    "Add a suffix to make the name unique (e.g., '_v2')",
                    "Delete the existing configuration if it's no longer needed"
                ]
            ),
            
            "INVALID_ENDPOINT_CONFIG": ErrorInfo(
                code="INVALID_ENDPOINT_CONFIG",
                category=ErrorCategory.CONFIGURATION,
                severity=ErrorSeverity.HIGH,
                title="Invalid Endpoint Configuration",
                message="One or more endpoints have invalid configuration",
                recovery_suggestions=[
                    "Check all endpoint URLs are valid",
                    "Verify HTTP methods are correct (GET, POST, etc.)",
                    "Ensure timeout values are reasonable (30-60 seconds)",
                    "Review endpoint paths for accuracy",
                    "Use the validation feature to identify specific issues"
                ]
            ),
            
            # System Errors
            "DATABASE_ERROR": ErrorInfo(
                code="DATABASE_ERROR",
                category=ErrorCategory.SYSTEM,
                severity=ErrorSeverity.CRITICAL,
                title="Database Error",
                message="Unable to save or retrieve configuration from database",
                recovery_suggestions=[
                    "Try the operation again in a few moments",
                    "Check system status and connectivity",
                    "Contact system administrator if problem persists"
                ],
                contact_support=True
            ),
            
            "ENCRYPTION_ERROR": ErrorInfo(
                code="ENCRYPTION_ERROR",
                category=ErrorCategory.SYSTEM,
                severity=ErrorSeverity.HIGH,
                title="Encryption Error",
                message="Unable to encrypt or decrypt sensitive configuration data",
                recovery_suggestions=[
                    "Check if encryption keys are properly configured",
                    "Verify system has proper encryption support",
                    "Contact system administrator for encryption key issues"
                ],
                contact_support=True
            ),
            
            # Import/Export Errors
            "INVALID_IMPORT_FORMAT": ErrorInfo(
                code="INVALID_IMPORT_FORMAT",
                category=ErrorCategory.USER_INPUT,
                severity=ErrorSeverity.MEDIUM,
                title="Invalid Import Format",
                message="The import data is not in a recognized format",
                recovery_suggestions=[
                    "Ensure data is in valid JSON or YAML format",
                    "Check for syntax errors in the data",
                    "Use data exported from this system for best compatibility",
                    "Validate your data with an online JSON/YAML validator"
                ]
            ),
            
            "IMPORT_VALIDATION_FAILED": ErrorInfo(
                code="IMPORT_VALIDATION_FAILED",
                category=ErrorCategory.VALIDATION,
                severity=ErrorSeverity.MEDIUM,
                title="Import Validation Failed",
                message="Some configurations in the import data failed validation",
                recovery_suggestions=[
                    "Review the validation errors for each configuration",
                    "Fix the issues in your import data",
                    "Import configurations one by one to isolate problems",
                    "Use templates to ensure proper format"
                ]
            )
        }
    
    def get_error_info(self, error_code: str) -> Optional[ErrorInfo]:
        """Get detailed error information by code"""
        return self._error_catalog.get(error_code)
    
    def categorize_error(self, error: Exception) -> ErrorInfo:
        """Categorize an exception and return appropriate error info"""
        error_str = str(error).lower()
        error_type = type(error).__name__
        
        # Connection-related errors
        if "timeout" in error_str or "timed out" in error_str:
            return self._error_catalog["CONNECTION_TIMEOUT"]
        elif "connection refused" in error_str or "refused" in error_str:
            return self._error_catalog["CONNECTION_REFUSED"]
        elif "dns" in error_str or "name resolution" in error_str:
            return self._error_catalog["DNS_RESOLUTION_FAILED"]
        
        # Authentication errors
        elif "401" in error_str or "unauthorized" in error_str:
            return self._error_catalog["INVALID_CREDENTIALS"]
        elif "403" in error_str or "forbidden" in error_str:
            return self._error_catalog["INSUFFICIENT_PERMISSIONS"]
        elif "token" in error_str and ("expired" in error_str or "invalid" in error_str):
            return self._error_catalog["TOKEN_EXPIRED"]
        
        # Validation errors
        elif error_type in ["ValueError", "ValidationError"]:
            if "url" in error_str:
                return self._error_catalog["INVALID_URL_FORMAT"]
            elif "service" in error_str and "name" in error_str:
                return self._error_catalog["INVALID_SERVICE_NAME"]
            else:
                return self._error_catalog["MISSING_REQUIRED_FIELD"]
        
        # Database errors
        elif "database" in error_str or "db" in error_str:
            return self._error_catalog["DATABASE_ERROR"]
        
        # Encryption errors
        elif "encrypt" in error_str or "decrypt" in error_str:
            return self._error_catalog["ENCRYPTION_ERROR"]
        
        # Default system error
        else:
            return ErrorInfo(
                code="UNKNOWN_ERROR",
                category=ErrorCategory.SYSTEM,
                severity=ErrorSeverity.MEDIUM,
                title="Unexpected Error",
                message=f"An unexpected error occurred: {str(error)}",
                recovery_suggestions=[
                    "Try the operation again",
                    "Check your input for any issues",
                    "Contact support if the problem persists"
                ],
                technical_details=f"Error type: {error_type}, Message: {str(error)}"
            )
    
    def format_error_message(self, error_info: ErrorInfo, include_technical: bool = False) -> str:
        """Format error information into a user-friendly message"""
        severity_icons = {
            ErrorSeverity.LOW: "ℹ️",
            ErrorSeverity.MEDIUM: "⚠️",
            ErrorSeverity.HIGH: "❌",
            ErrorSeverity.CRITICAL: "🚨"
        }
        
        icon = severity_icons.get(error_info.severity, "❌")
        
        message = f"{icon} <b>{error_info.title}</b>\n\n"
        message += f"{error_info.message}\n\n"
        
        if error_info.recovery_suggestions:
            message += "💡 <b>How to fix this:</b>\n"
            for suggestion in error_info.recovery_suggestions:
                message += f"• {suggestion}\n"
            message += "\n"
        
        if include_technical and error_info.technical_details:
            message += f"🔧 <b>Technical Details:</b>\n{error_info.technical_details}\n\n"
        
        if error_info.related_docs:
            message += f"📖 <b>Documentation:</b> {error_info.related_docs}\n\n"
        
        if error_info.contact_support:
            message += "🆘 <b>Need Help?</b> Contact system administrator if the problem persists."
        
        return message
    
    def get_recovery_actions(self, error_info: ErrorInfo) -> List[Dict[str, str]]:
        """Get actionable recovery steps for an error"""
        actions = []
        
        # Add common recovery actions based on error category
        if error_info.category == ErrorCategory.CONNECTION:
            actions.extend([
                {"action": "test_connection", "label": "🧪 Test Connection Again"},
                {"action": "edit_config", "label": "✏️ Edit Configuration"},
                {"action": "validate_config", "label": "🔍 Validate Settings"}
            ])
        elif error_info.category == ErrorCategory.AUTHENTICATION:
            actions.extend([
                {"action": "edit_credentials", "label": "🔑 Update Credentials"},
                {"action": "test_connection", "label": "🧪 Test Connection"},
                {"action": "view_docs", "label": "📖 View Documentation"}
            ])
        elif error_info.category == ErrorCategory.VALIDATION:
            actions.extend([
                {"action": "validate_config", "label": "🔍 Run Validation"},
                {"action": "edit_config", "label": "✏️ Edit Configuration"},
                {"action": "use_template", "label": "🎯 Use Template"}
            ])
        
        # Add general actions
        actions.extend([
            {"action": "get_help", "label": "❓ Get Help"},
            {"action": "back_to_main", "label": "⬅️ Back to Main Menu"}
        ])
        
        return actions


# Global instance
_error_service = None

def get_error_service() -> APIConfigErrorService:
    """Get the global error service instance"""
    global _error_service
    if _error_service is None:
        _error_service = APIConfigErrorService()
    return _error_service
