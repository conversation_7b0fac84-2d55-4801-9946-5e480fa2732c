"""
Card service for fetching and managing card data from external API
Enhanced with comprehensive API request/response logging for 403 error diagnosis
"""

from __future__ import annotations

import logging
import json
import time
from typing import Dict, List, Any, Optional
import aiohttp
import asyncio
from datetime import datetime

from config.settings import get_settings
from utils.performance import monitor_performance
from utils.api_logging import get_api_logger, LogLevel

logger = logging.getLogger(__name__)
api_logger = get_api_logger("card_service", LogLevel.DEBUG)


class CardService:
    """Service for fetching card data from external API"""

    def __init__(self):
        self.settings = get_settings()
        self.base_url = "https://ronaldo-club.to/api/cards/hq/list"
        self.session: Optional[aiohttp.ClientSession] = None

        # Default headers for API requests (based on demo/list.py)
        self.headers = {
            "accept": "application/json, text/plain, */*",
            "accept-language": "en-US,en;q=0.9",
            "content-length": "0",
            "origin": "https://ronaldo-club.to",
            "priority": "u=1, i",
            "referer": "https://ronaldo-club.to/store/cards/hq",
            "sec-ch-ua": '"Chromium";v="140", "Not=A?Brand";v="24", "Brave";v="140"',
            "sec-ch-ua-mobile": "?1",
            "sec-ch-ua-platform": '"Android"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "sec-gpc": "1",
            "user-agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/140.0.0.0 Mobile Safari/537.36",
        }

        # Default cookies (will be updated with login token)
        self.cookies = {
            "__ddg1_": "u1UaBqLkngSC4ZTJRDQC",
            "testcookie": "1",
        }

        # API configuration cache
        self._api_config = None
        self._config_loaded = False

    async def _load_api_config(self) -> None:
        """Load API configuration from the configuration service"""
        if self._config_loaded:
            return

        try:
            from services.api_config_service import get_api_config_service

            api_config_service = get_api_config_service()
            # Prefer new naming; fall back to legacy for compatibility
            self._api_config = await api_config_service.get_api_config("api1_external_cart")
            if not self._api_config:
                self._api_config = await api_config_service.get_api_config("external_cart")

            if self._api_config:
                logger.info("Loaded API configuration from service")

                # Update base URL if configured
                if self._api_config.base_url:
                    self.base_url = f"{self._api_config.base_url}/cards/hq/list"
                    logger.info(f"Updated base URL to: {self.base_url}")

                # Update headers if configured
                if (
                    self._api_config.credentials
                    and self._api_config.credentials.headers
                ):
                    self.headers.update(self._api_config.credentials.headers)
                    logger.info("Updated headers from API configuration")

                # Update cookies if configured
                if (
                    self._api_config.credentials
                    and self._api_config.credentials.session_cookies
                ):
                    self.cookies.update(self._api_config.credentials.session_cookies)
                    logger.info("Updated cookies from API configuration")

                # Add login token as cookie (key difference from previous implementation)
                if (
                    self._api_config.credentials
                    and self._api_config.credentials.login_token
                ):
                    self.cookies["loginToken"] = (
                        self._api_config.credentials.login_token
                    )
                    logger.info("Added loginToken cookie from API configuration")

            else:
                logger.warning(
                    "No API configuration found for 'external_cart', using defaults"
                )

        except Exception as e:
            logger.error(f"Failed to load API configuration: {e}")

        # Fallback: If no login token configured, use the working JWT token directly
        if "loginToken" not in self.cookies:
            jwt_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTk5MDk0LCJpYXQiOjE3NTczNTQ3NDYsImV4cCI6MTc1OTk0Njc0Nn0.iXL_3zO-VJ2elJ4furlJ_FVSZONnRwogtQdZofX3M6o"
            self.cookies["loginToken"] = jwt_token
            # Use cart-compatible cookies from demo/add_to_cart.py for better compatibility
            self.cookies.update(
                {
                    "__ddg8_": "VIywnRkIRMee1yvS",  # Cart-compatible values
                    "__ddg9_": "118.99.2.9",  # Cart-compatible values
                    "__ddg10_": "1757322900",  # Cart-compatible values
                }
            )
            logger.info(
                "Applied working JWT token and cart-compatible demo cookies as fallback"
            )
            # Continue with defaults

        self._config_loaded = True

    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session with API configuration"""
        # Load API configuration if not already loaded
        await self._load_api_config()

        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(
                headers=self.headers, cookies=self.cookies, timeout=timeout
            )
            logger.debug(
                f"Created new session with {len(self.headers)} headers and {len(self.cookies)} cookies"
            )
        return self.session

    async def close(self) -> None:
        """Close the aiohttp session"""
        if self.session and not self.session.closed:
            await self.session.close()

    def _apply_filters_to_params(
        self, params: Dict[str, str], filters: Dict[str, Any]
    ) -> None:
        """
        Apply filters to API parameters with proper validation and mapping

        Args:
            params: API parameters dictionary to modify
            filters: User filters to apply
        """
        # Filter mapping from UI filter names to API parameter names
        filter_mapping = {
            "bank": "bank",
            "country": "country",
            "brand": "brand",
            "type": "type",
            "priceFrom": "priceFrom",
            "priceTo": "priceTo",
            "base": "base",
            "bin": "bin",
            "state": "state",
            "city": "city",
            "zip": "zip",
        }

        # Boolean filters mapping
        boolean_filters = {
            "zipCheck": "zipCheck",
            "address": "address",
            "phone": "phone",
            "email": "email",
            "withoutcvv": "withoutcvv",
            "refundable": "refundable",
            "expirethismonth": "expirethismonth",
            "dob": "dob",
            "ssn": "ssn",
            "mmn": "mmn",
            "ip": "ip",
            "dl": "dl",
            "ua": "ua",
            "discount": "discount",
        }

        # Apply string/value filters
        for filter_key, param_key in filter_mapping.items():
            if filter_key in filters and filters[filter_key] is not None:
                value = filters[filter_key]
                # Clean and validate the value
                if isinstance(value, str):
                    value = value.strip()
                if value:  # Only apply non-empty values
                    params[param_key] = str(value)
                    logger.debug(
                        f"Applied filter {filter_key}={value} to param {param_key}"
                    )

        # Apply boolean filters
        for filter_key, param_key in boolean_filters.items():
            if filter_key in filters and filters[filter_key] is not None:
                value = filters[filter_key]
                # Convert to string boolean
                if isinstance(value, bool):
                    params[param_key] = "true" if value else "false"
                elif isinstance(value, str) and value.lower() in ("true", "false"):
                    params[param_key] = value.lower()
                logger.debug(
                    f"Applied boolean filter {filter_key}={params[param_key]} to param {param_key}"
                )

    @monitor_performance("fetch_cards")
    async def fetch_cards(
        self,
        page: int = 1,
        limit: int = 10,
        filters: Optional[Dict[str, Any]] = None,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Fetch cards from the external API with comprehensive logging

        Args:
            page: Page number (default: 1)
            limit: Number of items per page (default: 10)
            filters: Optional filters to apply
            user_id: User ID for logging context

        Returns:
            Dictionary containing success status, data, and metadata
        """
        # Create logging context
        context = api_logger.create_context(user_id=user_id, operation="fetch_cards")

        try:
            # Build query parameters with defaults
            params = {
                "page": str(page),
                "limit": str(limit),
                "base": "",
                "bank": "",
                "bin": "",
                "country": "",
                "state": "",
                "city": "",
                "brand": "",
                "type": "",
                "zip": "",
                "priceFrom": "0",
                "priceTo": "5",
                "zipCheck": "false",
                "address": "false",
                "phone": "false",
                "email": "false",
                "withoutcvv": "false",
                "refundable": "false",
                "expirethismonth": "false",
                "dob": "false",
                "ssn": "false",
                "mmn": "false",
                "ip": "false",
                "dl": "false",
                "ua": "false",
                "discount": "false",
            }

            # Apply filters if provided with proper validation and mapping
            if filters:
                self._apply_filters_to_params(params, filters)
                logger.info(f"Applied filters: {filters}")

            session = await self._get_session()

            # Log authentication context
            auth_method = "session_cookies" if self.cookies else "none"
            token_valid = bool(
                self.cookies.get("loginToken")
                or self._api_config
                and self._api_config.credentials
                and self._api_config.credentials.login_token
            )

            api_logger.log_authentication_context(
                context=context,
                auth_method=auth_method,
                token_valid=token_valid,
                user_permissions=["read_cards"] if token_valid else [],
                rate_limit_info={},
            )

            # Log the API request
            api_logger.log_request(
                context=context,
                method="POST",
                url=self.base_url,
                headers=self.headers,
                query_params=params,
                body="",  # Empty body as per original implementation
                timeout=30.0,
            )

            # Make POST request with empty body (as per original implementation)
            start_time = time.time()
            async with session.post(self.base_url, params=params, data=b"") as response:
                response_text = await response.text()
                response_headers = dict(response.headers)

                # Log the API response
                api_logger.log_response(
                    context=context,
                    status_code=response.status,
                    status_message=response.reason or "Unknown",
                    headers=response_headers,
                    body=response_text,
                    error_type="http_error" if response.status >= 400 else None,
                    error_message=(
                        f"HTTP {response.status}" if response.status >= 400 else None
                    ),
                )

                if response.status == 200:
                    try:
                        data = await response.json()
                        cards_count = len(data.get("data", []))
                        total_count = data.get("totalCount", 0)

                        logger.info(
                            f"Successfully fetched {cards_count} cards (page {page}, total: {total_count})"
                        )

                        # Ensure the response has the expected structure
                        if "data" not in data:
                            data["data"] = []
                        if "totalCount" not in data:
                            data["totalCount"] = 0
                        if "success" not in data:
                            data["success"] = True

                        return data
                    except Exception as json_error:
                        logger.error(f"Failed to parse JSON response: {json_error}")
                        logger.debug(f"Response text: {response_text[:500]}...")

                        # Log JSON parsing error
                        api_logger.log_response(
                            context=context,
                            status_code=200,
                            status_message="JSON Parse Error",
                            headers=response_headers,
                            body=response_text[:1000],  # Limit body size
                            error_type="json_parse_error",
                            error_message=str(json_error),
                        )

                        return {
                            "success": False,
                            "error": "Invalid JSON response from API",
                            "data": [],
                            "totalCount": 0,
                        }
                elif response.status == 403:
                    # Special handling for 403 Forbidden errors
                    api_logger.log_403_error_context(
                        context=context,
                        endpoint=self.base_url,
                        auth_method=auth_method,
                        token_status="valid" if token_valid else "invalid",
                        user_role="user" if token_valid else "anonymous",
                        required_permissions=["read_cards"],
                        rate_limit_headers={
                            k: v
                            for k, v in response_headers.items()
                            if k.lower().startswith(
                                ("x-rate", "x-ratelimit", "retry-after")
                            )
                        },
                    )

                    logger.error(
                        f"403 Forbidden: API request denied - {response_text[:200]}"
                    )
                    return {
                        "success": False,
                        "error": f"Access denied (403): {response_text[:100]}",
                        "data": [],
                        "totalCount": 0,
                    }
                else:
                    logger.error(f"API request failed with status {response.status}")
                    logger.debug(f"Response text: {response_text[:500]}...")
                    return {
                        "success": False,
                        "error": f"API request failed with status {response.status}",
                        "data": [],
                        "totalCount": 0,
                    }

        except asyncio.TimeoutError:
            logger.error("API request timed out")

            # Log timeout error
            api_logger.log_response(
                context=context,
                status_code=408,
                status_message="Request Timeout",
                headers={},
                body=None,
                error_type="timeout_error",
                error_message="Request timed out after 30 seconds",
            )

            return {
                "success": False,
                "error": "Request timed out",
                "data": [],
                "totalCount": 0,
            }
        except Exception as e:
            logger.error(f"Error fetching cards: {e}")

            # Log general error
            api_logger.log_response(
                context=context,
                status_code=500,
                status_message="Internal Error",
                headers={},
                body=None,
                error_type="exception",
                error_message=str(e),
            )

            return {"success": False, "error": str(e), "data": [], "totalCount": 0}

    async def search_cards(
        self,
        bank: Optional[str] = None,
        country: Optional[str] = None,
        card_type: Optional[str] = None,
        brand: Optional[str] = None,
        price_from: Optional[float] = None,
        price_to: Optional[float] = None,
        page: int = 1,
        limit: int = 10,
    ) -> Dict[str, Any]:
        """
        Search cards with specific criteria

        Args:
            bank: Bank name filter
            country: Country code filter
            card_type: Card type filter (CREDIT/DEBIT)
            brand: Card brand filter
            price_from: Minimum price filter
            price_to: Maximum price filter
            page: Page number
            limit: Items per page

        Returns:
            Dictionary containing search results
        """
        filters = {}

        if bank:
            filters["bank"] = bank
        if country:
            filters["country"] = country
        if card_type:
            filters["type"] = card_type
        if brand:
            filters["brand"] = brand
        if price_from is not None:
            filters["priceFrom"] = str(price_from)
        if price_to is not None:
            filters["priceTo"] = str(price_to)

        return await self.fetch_cards(page=page, limit=limit, filters=filters)

    def format_card_for_display(self, card: Dict[str, Any]) -> str:
        """
        Format a card object for display in Telegram

        Args:
            card: Card data dictionary

        Returns:
            Formatted string for display
        """
        try:
            # Extract key information
            card_id = card.get("_id", "N/A")
            bank = card.get("bank", "Unknown Bank")
            bin_number = card.get("bin", "N/A")
            card_type = card.get("type", "N/A")
            level = card.get("level", "N/A")
            country = card.get("country", "N/A")
            price = card.get("price", "0.00")
            exp = card.get("exp", "N/A")
            refund_rate = card.get("refund_rate", "0.00")

            # Format the card information
            formatted = (
                f"💳 <b>Card #{card_id}</b>\n"
                f"🏦 <b>Bank:</b> {bank}\n"
                f"🔢 <b>BIN:</b> {bin_number}\n"
                f"📊 <b>Type:</b> {card_type} ({level})\n"
                f"🌍 <b>Country:</b> {country}\n"
                f"📅 <b>Expires:</b> {exp}\n"
                f"💰 <b>Price:</b> ${price}\n"
                f"📈 <b>Refund Rate:</b> {refund_rate}%\n"
            )

            # Add additional features if available
            features = []
            if card.get("address"):
                features.append("📍 Address")
            if card.get("phone"):
                features.append("📞 Phone")
            if card.get("email"):
                features.append("📧 Email")
            if card.get("ip"):
                features.append("🌐 IP")
            if card.get("refundable"):
                features.append("🔄 Refundable")

            if features:
                formatted += f"✨ <b>Features:</b> {', '.join(features)}\n"

            return formatted

        except Exception as e:
            logger.error(f"Error formatting card for display: {e}")
            return f"💳 Card #{card.get('_id', 'N/A')} - Error displaying details"

    async def get_card_summary(self, cards_data: Dict[str, Any]) -> str:
        """
        Generate a summary of the cards data

        Args:
            cards_data: Response from fetch_cards

        Returns:
            Formatted summary string
        """
        try:
            if not cards_data.get("success", False):
                return "❌ Failed to fetch card data"

            cards = cards_data.get("data", [])
            total_count = cards_data.get("totalCount", 0)
            limit = cards_data.get("limit", 10)

            if not cards:
                return "📭 No cards found matching your criteria"

            # Generate statistics
            card_types = {}
            banks = {}
            countries = {}

            for card in cards:
                # Count card types
                card_type = card.get("type", "Unknown")
                card_types[card_type] = card_types.get(card_type, 0) + 1

                # Count banks
                bank = card.get("bank", "Unknown")
                banks[bank] = banks.get(bank, 0) + 1

                # Count countries
                country = card.get("country", "Unknown")
                countries[country] = countries.get(country, 0) + 1

            summary = (
                f"📊 <b>Card Catalog Summary</b>\n\n"
                f"📈 <b>Total Available:</b> {total_count:,} cards\n"
                f"📄 <b>Showing:</b> {len(cards)} of {total_count:,}\n\n"
                f"🏦 <b>Top Banks:</b>\n"
            )

            # Show top 3 banks
            top_banks = sorted(banks.items(), key=lambda x: x[1], reverse=True)[:3]
            for bank, count in top_banks:
                bank_short = bank[:30] + "..." if len(bank) > 30 else bank
                summary += f"  • {bank_short}: {count}\n"

            summary += f"\n💳 <b>Card Types:</b>\n"
            for card_type, count in sorted(card_types.items()):
                summary += f"  • {card_type}: {count}\n"

            summary += f"\n🌍 <b>Countries:</b> {', '.join(sorted(countries.keys()))}\n"

            return summary

        except Exception as e:
            logger.error(f"Error generating card summary: {e}")
            return "❌ Error generating summary"
