"""
API Health Monitoring Service for periodic health checks, metrics collection,
and alerting for API configurations.
"""

from __future__ import annotations

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import aiohttp
import time

from models.api import (
    APIConfiguration,
    APIHealthStatus,
    APIUsageMetrics,
    APIRequestLog,
    APIStatus,
    HTTPMethod,
)
from models.base import now_utc
from database.connection import get_collection
from services.api_service import APIConfigurationService
from utils.api_ui_helpers import format_bytes

logger = logging.getLogger(__name__)


class APIHealthMonitor:
    """Service for monitoring API health and collecting metrics"""
    
    def __init__(self):
        self.api_service = APIConfigurationService()
        self.api_configs = get_collection("api_configurations")
        self.api_health = get_collection("api_health_status")
        self.api_metrics = get_collection("api_usage_metrics")
        self.api_logs = get_collection("api_request_logs")
        self._monitoring_task = None
        self._is_running = False
    
    async def start_monitoring(self) -> None:
        """Start the health monitoring background task"""
        if self._is_running:
            logger.warning("Health monitoring is already running")
            return
        
        self._is_running = True
        self._monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("API health monitoring started")
    
    async def stop_monitoring(self) -> None:
        """Stop the health monitoring background task"""
        self._is_running = False
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        logger.info("API health monitoring stopped")
    
    async def _monitoring_loop(self) -> None:
        """Main monitoring loop that runs health checks"""
        while self._is_running:
            try:
                await self._run_scheduled_health_checks()
                await self._collect_usage_metrics()
                await self._cleanup_old_data()
                
                # Wait for next check cycle (1 minute)
                await asyncio.sleep(60)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(60)  # Continue after error
    
    async def _run_scheduled_health_checks(self) -> None:
        """Run health checks for APIs that are due for checking"""
        try:
            current_time = now_utc()
            
            # Find APIs due for health check
            cursor = self.api_health.find({
                "next_check_at": {"$lte": current_time}
            })
            
            health_statuses = await cursor.to_list(None)
            
            for health_doc in health_statuses:
                health_status = APIHealthStatus.from_mongo(health_doc)
                await self._perform_health_check(health_status)
                
        except Exception as e:
            logger.error(f"Error running scheduled health checks: {e}")
    
    async def _perform_health_check(self, health_status: APIHealthStatus) -> None:
        """Perform health check for a specific API"""
        try:
            # Get API configuration
            config = await self.api_service.get_api_config(
                health_status.api_config_id, 
                decrypt_sensitive=True
            )
            
            if not config or not config.health_check.enabled:
                return
            
            start_time = time.time()
            
            # Build test URL
            test_url = config.base_url
            if config.health_check.endpoint:
                test_url += config.health_check.endpoint
            
            # Prepare headers
            headers = config.default_headers.copy()
            auth_headers = self.api_service._build_auth_headers(config.authentication)
            headers.update(auth_headers)
            
            # Perform health check
            try:
                async with aiohttp.ClientSession(
                    timeout=aiohttp.ClientTimeout(total=config.health_check.timeout_seconds)
                ) as session:
                    async with session.request(
                        config.health_check.method.value,
                        test_url,
                        headers=headers,
                        params=config.default_params
                    ) as response:
                        response_time = int((time.time() - start_time) * 1000)
                        response_text = await response.text()
                        
                        # Check if response meets health criteria
                        is_healthy = (
                            response.status in config.health_check.expected_status_codes and
                            (not config.health_check.expected_response_contains or 
                             config.health_check.expected_response_contains in response_text)
                        )
                        
                        # Update health status
                        await self._update_health_status(
                            health_status,
                            is_healthy=is_healthy,
                            response_time_ms=response_time,
                            status_code=response.status,
                            error_message=None
                        )
                        
                        # Log the health check
                        await self._log_health_check(
                            config.id,
                            is_healthy,
                            response_time,
                            response.status,
                            len(response_text)
                        )
            
            except Exception as e:
                # Health check failed
                response_time = int((time.time() - start_time) * 1000)
                await self._update_health_status(
                    health_status,
                    is_healthy=False,
                    response_time_ms=response_time,
                    status_code=None,
                    error_message=str(e)
                )
                
                # Log the failed health check
                await self._log_health_check(
                    config.id,
                    False,
                    response_time,
                    None,
                    0,
                    str(e)
                )
                
        except Exception as e:
            logger.error(f"Error performing health check for {health_status.api_config_id}: {e}")
    
    async def _update_health_status(
        self,
        health_status: APIHealthStatus,
        is_healthy: bool,
        response_time_ms: int,
        status_code: Optional[int],
        error_message: Optional[str]
    ) -> None:
        """Update health status in database"""
        try:
            current_time = now_utc()
            
            # Get API config for health check interval
            config = await self.api_service.get_api_config(health_status.api_config_id)
            if not config:
                return
            
            # Calculate next check time
            next_check = current_time + timedelta(minutes=config.health_check.interval_minutes)
            
            # Update counters
            if is_healthy:
                consecutive_successes = health_status.consecutive_successes + 1
                consecutive_failures = 0
                
                # Check if we should mark as healthy
                if consecutive_successes >= config.health_check.success_threshold:
                    was_unhealthy = not health_status.is_healthy
                    health_status.is_healthy = True
                    
                    # If API was unhealthy and now healthy, update API status
                    if was_unhealthy and config.status == APIStatus.ERROR:
                        await self.api_service.update_api_config(
                            health_status.api_config_id,
                            {"status": APIStatus.ACTIVE.value},
                            "health_monitor"
                        )
            else:
                consecutive_failures = health_status.consecutive_failures + 1
                consecutive_successes = 0
                health_status.total_failures += 1
                
                # Check if we should mark as unhealthy
                if consecutive_failures >= config.health_check.failure_threshold:
                    health_status.is_healthy = False
                    
                    # Update API status to error if it was active
                    if config.status == APIStatus.ACTIVE:
                        await self.api_service.update_api_config(
                            health_status.api_config_id,
                            {"status": APIStatus.ERROR.value},
                            "health_monitor"
                        )
            
            # Calculate uptime percentage
            health_status.total_checks += 1
            health_status.uptime_percentage = (
                (health_status.total_checks - health_status.total_failures) / 
                health_status.total_checks * 100
            )
            
            # Update fields
            health_status.consecutive_successes = consecutive_successes
            health_status.consecutive_failures = consecutive_failures
            health_status.last_check_at = current_time
            health_status.next_check_at = next_check
            health_status.response_time_ms = response_time_ms
            health_status.status_code = status_code
            health_status.error_message = error_message
            
            if not is_healthy and health_status.last_downtime_at is None:
                health_status.last_downtime_at = current_time
            
            # Save to database
            await self.api_health.update_one(
                {"_id": health_status.id},
                {"$set": health_status.to_mongo()}
            )
            
        except Exception as e:
            logger.error(f"Error updating health status: {e}")
    
    async def _log_health_check(
        self,
        api_config_id: str,
        success: bool,
        response_time_ms: int,
        status_code: Optional[int],
        response_size_bytes: int,
        error_message: Optional[str] = None
    ) -> None:
        """Log health check result"""
        try:
            log_entry = APIRequestLog(
                api_config_id=api_config_id,
                method=HTTPMethod.GET,
                url="[HEALTH_CHECK]",
                status_code=status_code,
                response_time_ms=response_time_ms,
                response_size_bytes=response_size_bytes,
                error_type="health_check_failure" if not success else None,
                error_message=error_message,
                initiated_by="health_monitor",
                created_at=now_utc()
            )
            
            await self.api_logs.insert_one(log_entry.to_mongo())
            
        except Exception as e:
            logger.error(f"Error logging health check: {e}")
    
    async def get_health_dashboard_data(self) -> Dict[str, Any]:
        """Get comprehensive health dashboard data"""
        try:
            # Get all health statuses
            cursor = self.api_health.find({})
            health_docs = await cursor.to_list(None)
            health_statuses = [APIHealthStatus.from_mongo(doc) for doc in health_docs]
            
            # Calculate overall statistics
            total_apis = len(health_statuses)
            healthy_apis = sum(1 for h in health_statuses if h.is_healthy)
            unhealthy_apis = total_apis - healthy_apis
            
            # Calculate average uptime
            avg_uptime = sum(h.uptime_percentage for h in health_statuses) / total_apis if total_apis > 0 else 100.0
            
            # Get APIs with recent failures
            recent_failures = [h for h in health_statuses if h.consecutive_failures > 0]
            
            # Get slowest APIs (by response time)
            slowest_apis = sorted(
                [h for h in health_statuses if h.response_time_ms],
                key=lambda x: x.response_time_ms or 0,
                reverse=True
            )[:5]
            
            return {
                "total_apis": total_apis,
                "healthy_apis": healthy_apis,
                "unhealthy_apis": unhealthy_apis,
                "avg_uptime": avg_uptime,
                "recent_failures": recent_failures,
                "slowest_apis": slowest_apis,
                "health_statuses": health_statuses
            }
            
        except Exception as e:
            logger.error(f"Error getting health dashboard data: {e}")
            return {
                "total_apis": 0,
                "healthy_apis": 0,
                "unhealthy_apis": 0,
                "avg_uptime": 0.0,
                "recent_failures": [],
                "slowest_apis": [],
                "health_statuses": []
            }
