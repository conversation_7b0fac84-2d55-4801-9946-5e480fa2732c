"""
API Analytics Service for usage tracking, reporting, and insights
"""

from __future__ import annotations

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import json
from collections import defaultdict

from models.api import (
    APIConfiguration,
    APIUsageMetrics,
    APIRequestLog,
    APIHealthStatus,
    APIEnvironment,
    APIStatus,
    HTTPMethod
)
from models.base import now_utc
from database.connection import get_collection
from services.api_service import APIConfigurationService

logger = logging.getLogger(__name__)


class APIAnalyticsService:
    """Service for API usage analytics and reporting"""
    
    def __init__(self):
        self.api_service = APIConfigurationService()
        self.api_configs = get_collection("api_configurations")
        self.api_metrics = get_collection("api_usage_metrics")
        self.api_logs = get_collection("api_request_logs")
        self.api_health = get_collection("api_health_status")
    
    async def get_dashboard_analytics(self) -> Dict[str, Any]:
        """Get comprehensive analytics for the dashboard"""
        try:
            # Get all API configurations
            configs, total_apis = await self.api_service.list_api_configs(page=1, per_page=1000)
            
            # Get health statuses
            health_cursor = self.api_health.find({})
            health_docs = await health_cursor.to_list(None)
            health_by_api = {doc["api_config_id"]: doc for doc in health_docs}
            
            # Calculate basic stats
            active_apis = sum(1 for c in configs if c.status == APIStatus.ACTIVE)
            healthy_apis = sum(1 for h in health_docs if h.get("is_healthy", False))
            
            # Environment breakdown
            env_stats = defaultdict(int)
            for config in configs:
                env_stats[config.environment.value] += 1
            
            # Status breakdown
            status_stats = defaultdict(int)
            for config in configs:
                status_stats[config.status.value] += 1
            
            # Auth type breakdown
            auth_stats = defaultdict(int)
            for config in configs:
                auth_stats[config.authentication.type.value] += 1
            
            # Get recent metrics (last 24 hours)
            recent_metrics = await self._get_recent_metrics_summary(hours=24)
            
            # Get top performing APIs
            top_apis = await self._get_top_performing_apis(limit=5)
            
            # Get APIs with issues
            problematic_apis = await self._get_problematic_apis(limit=5)
            
            return {
                "overview": {
                    "total_apis": total_apis,
                    "active_apis": active_apis,
                    "healthy_apis": healthy_apis,
                    "health_percentage": (healthy_apis / total_apis * 100) if total_apis > 0 else 0,
                    "last_updated": now_utc()
                },
                "breakdown": {
                    "environments": dict(env_stats),
                    "statuses": dict(status_stats),
                    "auth_types": dict(auth_stats)
                },
                "recent_metrics": recent_metrics,
                "top_apis": top_apis,
                "problematic_apis": problematic_apis
            }
            
        except Exception as e:
            logger.error(f"Failed to get dashboard analytics: {e}")
            return self._empty_analytics()
    
    async def get_api_detailed_analytics(
        self, 
        config_id: str, 
        days: int = 7
    ) -> Dict[str, Any]:
        """Get detailed analytics for a specific API"""
        try:
            config = await self.api_service.get_api_config(config_id)
            if not config:
                return {"error": "API configuration not found"}
            
            end_date = now_utc()
            start_date = end_date - timedelta(days=days)
            
            # Get metrics for the period
            metrics_cursor = self.api_metrics.find({
                "api_config_id": config_id,
                "period_start": {"$gte": start_date, "$lte": end_date}
            }).sort("period_start", 1)
            
            metrics_docs = await metrics_cursor.to_list(None)
            metrics = [APIUsageMetrics.from_mongo(doc) for doc in metrics_docs]
            
            # Get health status
            health_doc = await self.api_health.find_one({"api_config_id": config_id})
            health = APIHealthStatus.from_mongo(health_doc) if health_doc else None
            
            # Calculate aggregated metrics
            total_requests = sum(m.total_requests for m in metrics)
            total_successful = sum(m.successful_requests for m in metrics)
            total_failed = sum(m.failed_requests for m in metrics)
            
            avg_response_time = (
                sum(m.avg_response_time_ms * m.total_requests for m in metrics) / total_requests
                if total_requests > 0 else 0
            )
            
            # Get hourly breakdown for charts
            hourly_data = await self._get_hourly_breakdown(config_id, days)
            
            # Get error analysis
            error_analysis = await self._get_error_analysis(config_id, days)
            
            # Get performance trends
            performance_trends = await self._get_performance_trends(config_id, days)
            
            return {
                "config": {
                    "id": str(config.id),
                    "name": config.name,
                    "base_url": config.base_url,
                    "environment": config.environment.value,
                    "status": config.status.value
                },
                "health": {
                    "is_healthy": health.is_healthy if health else False,
                    "uptime_percentage": health.uptime_percentage if health else 0,
                    "consecutive_failures": health.consecutive_failures if health else 0,
                    "last_check_at": health.last_check_at if health else None,
                    "response_time_ms": health.response_time_ms if health else None
                },
                "usage_summary": {
                    "total_requests": total_requests,
                    "successful_requests": total_successful,
                    "failed_requests": total_failed,
                    "success_rate": (total_successful / total_requests * 100) if total_requests > 0 else 0,
                    "avg_response_time_ms": avg_response_time,
                    "period_days": days
                },
                "hourly_data": hourly_data,
                "error_analysis": error_analysis,
                "performance_trends": performance_trends
            }
            
        except Exception as e:
            logger.error(f"Failed to get detailed analytics for {config_id}: {e}")
            return {"error": str(e)}
    
    async def _get_recent_metrics_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get summary of recent metrics across all APIs"""
        try:
            end_date = now_utc()
            start_date = end_date - timedelta(hours=hours)
            
            # Aggregate metrics from all APIs
            pipeline = [
                {
                    "$match": {
                        "period_start": {"$gte": start_date, "$lte": end_date}
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "total_requests": {"$sum": "$total_requests"},
                        "successful_requests": {"$sum": "$successful_requests"},
                        "failed_requests": {"$sum": "$failed_requests"},
                        "avg_response_time": {"$avg": "$avg_response_time_ms"},
                        "total_bytes_sent": {"$sum": "$total_bytes_sent"},
                        "total_bytes_received": {"$sum": "$total_bytes_received"}
                    }
                }
            ]
            
            cursor = self.api_metrics.aggregate(pipeline)
            result = await cursor.to_list(1)
            
            if result:
                data = result[0]
                return {
                    "total_requests": data.get("total_requests", 0),
                    "successful_requests": data.get("successful_requests", 0),
                    "failed_requests": data.get("failed_requests", 0),
                    "success_rate": (
                        data.get("successful_requests", 0) / data.get("total_requests", 1) * 100
                        if data.get("total_requests", 0) > 0 else 0
                    ),
                    "avg_response_time_ms": data.get("avg_response_time", 0),
                    "total_bytes_sent": data.get("total_bytes_sent", 0),
                    "total_bytes_received": data.get("total_bytes_received", 0),
                    "period_hours": hours
                }
            
            return {
                "total_requests": 0,
                "successful_requests": 0,
                "failed_requests": 0,
                "success_rate": 0,
                "avg_response_time_ms": 0,
                "total_bytes_sent": 0,
                "total_bytes_received": 0,
                "period_hours": hours
            }
            
        except Exception as e:
            logger.error(f"Failed to get recent metrics summary: {e}")
            return {}
    
    async def _get_top_performing_apis(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get top performing APIs by success rate and response time"""
        try:
            # Get recent metrics for all APIs
            end_date = now_utc()
            start_date = end_date - timedelta(days=7)
            
            pipeline = [
                {
                    "$match": {
                        "period_start": {"$gte": start_date, "$lte": end_date}
                    }
                },
                {
                    "$group": {
                        "_id": "$api_config_id",
                        "total_requests": {"$sum": "$total_requests"},
                        "successful_requests": {"$sum": "$successful_requests"},
                        "avg_response_time": {"$avg": "$avg_response_time_ms"}
                    }
                },
                {
                    "$addFields": {
                        "success_rate": {
                            "$cond": [
                                {"$gt": ["$total_requests", 0]},
                                {"$multiply": [{"$divide": ["$successful_requests", "$total_requests"]}, 100]},
                                0
                            ]
                        }
                    }
                },
                {
                    "$sort": {"success_rate": -1, "avg_response_time": 1}
                },
                {
                    "$limit": limit
                }
            ]
            
            cursor = self.api_metrics.aggregate(pipeline)
            results = await cursor.to_list(limit)
            
            # Enrich with API configuration data
            top_apis = []
            for result in results:
                config = await self.api_service.get_api_config(result["_id"])
                if config:
                    top_apis.append({
                        "config_id": result["_id"],
                        "name": config.name,
                        "base_url": config.base_url,
                        "total_requests": result["total_requests"],
                        "success_rate": result["success_rate"],
                        "avg_response_time_ms": result["avg_response_time"]
                    })
            
            return top_apis
            
        except Exception as e:
            logger.error(f"Failed to get top performing APIs: {e}")
            return []
    
    async def _get_problematic_apis(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get APIs with the most issues"""
        try:
            # Get APIs with low success rates or high error counts
            end_date = now_utc()
            start_date = end_date - timedelta(days=7)
            
            pipeline = [
                {
                    "$match": {
                        "period_start": {"$gte": start_date, "$lte": end_date}
                    }
                },
                {
                    "$group": {
                        "_id": "$api_config_id",
                        "total_requests": {"$sum": "$total_requests"},
                        "failed_requests": {"$sum": "$failed_requests"},
                        "avg_response_time": {"$avg": "$avg_response_time_ms"},
                        "timeout_count": {"$sum": "$timeout_count"}
                    }
                },
                {
                    "$addFields": {
                        "error_rate": {
                            "$cond": [
                                {"$gt": ["$total_requests", 0]},
                                {"$multiply": [{"$divide": ["$failed_requests", "$total_requests"]}, 100]},
                                0
                            ]
                        }
                    }
                },
                {
                    "$match": {
                        "$or": [
                            {"error_rate": {"$gt": 5}},  # More than 5% error rate
                            {"avg_response_time": {"$gt": 5000}},  # Slower than 5 seconds
                            {"timeout_count": {"$gt": 0}}  # Any timeouts
                        ]
                    }
                },
                {
                    "$sort": {"error_rate": -1, "avg_response_time": -1}
                },
                {
                    "$limit": limit
                }
            ]
            
            cursor = self.api_metrics.aggregate(pipeline)
            results = await cursor.to_list(limit)
            
            # Enrich with API configuration data
            problematic_apis = []
            for result in results:
                config = await self.api_service.get_api_config(result["_id"])
                if config:
                    problematic_apis.append({
                        "config_id": result["_id"],
                        "name": config.name,
                        "base_url": config.base_url,
                        "total_requests": result["total_requests"],
                        "error_rate": result["error_rate"],
                        "avg_response_time_ms": result["avg_response_time"],
                        "timeout_count": result["timeout_count"]
                    })
            
            return problematic_apis
            
        except Exception as e:
            logger.error(f"Failed to get problematic APIs: {e}")
            return []
    
    async def _get_hourly_breakdown(self, config_id: str, days: int) -> List[Dict[str, Any]]:
        """Get hourly breakdown of API usage"""
        try:
            end_date = now_utc()
            start_date = end_date - timedelta(days=days)
            
            cursor = self.api_metrics.find({
                "api_config_id": config_id,
                "period_type": "hourly",
                "period_start": {"$gte": start_date, "$lte": end_date}
            }).sort("period_start", 1)
            
            docs = await cursor.to_list(None)
            
            return [
                {
                    "timestamp": doc["period_start"],
                    "requests": doc.get("total_requests", 0),
                    "success_rate": (
                        doc.get("successful_requests", 0) / doc.get("total_requests", 1) * 100
                        if doc.get("total_requests", 0) > 0 else 0
                    ),
                    "avg_response_time": doc.get("avg_response_time_ms", 0)
                }
                for doc in docs
            ]
            
        except Exception as e:
            logger.error(f"Failed to get hourly breakdown for {config_id}: {e}")
            return []
    
    async def _get_error_analysis(self, config_id: str, days: int) -> Dict[str, Any]:
        """Get error analysis for an API"""
        try:
            end_date = now_utc()
            start_date = end_date - timedelta(days=days)
            
            # Get error logs
            cursor = self.api_logs.find({
                "api_config_id": config_id,
                "created_at": {"$gte": start_date, "$lte": end_date},
                "status_code": {"$gte": 400}
            })
            
            error_logs = await cursor.to_list(None)
            
            # Analyze error patterns
            status_code_counts = defaultdict(int)
            error_type_counts = defaultdict(int)
            hourly_errors = defaultdict(int)
            
            for log in error_logs:
                status_code = log.get("status_code")
                if status_code:
                    status_code_counts[status_code] += 1
                
                error_type = log.get("error_type", "unknown")
                error_type_counts[error_type] += 1
                
                # Group by hour for trend analysis
                hour_key = log["created_at"].strftime("%Y-%m-%d %H:00")
                hourly_errors[hour_key] += 1
            
            return {
                "total_errors": len(error_logs),
                "status_code_breakdown": dict(status_code_counts),
                "error_type_breakdown": dict(error_type_counts),
                "hourly_error_trend": dict(hourly_errors),
                "most_common_error": max(status_code_counts.items(), key=lambda x: x[1])[0] if status_code_counts else None
            }
            
        except Exception as e:
            logger.error(f"Failed to get error analysis for {config_id}: {e}")
            return {}
    
    async def _get_performance_trends(self, config_id: str, days: int) -> Dict[str, Any]:
        """Get performance trends for an API"""
        try:
            end_date = now_utc()
            start_date = end_date - timedelta(days=days)
            
            # Get daily metrics
            cursor = self.api_metrics.find({
                "api_config_id": config_id,
                "period_type": "daily",
                "period_start": {"$gte": start_date, "$lte": end_date}
            }).sort("period_start", 1)
            
            daily_metrics = await cursor.to_list(None)
            
            if not daily_metrics:
                return {}
            
            # Calculate trends
            response_times = [m.get("avg_response_time_ms", 0) for m in daily_metrics]
            request_volumes = [m.get("total_requests", 0) for m in daily_metrics]
            error_rates = [m.get("error_rate", 0) for m in daily_metrics]
            
            # Calculate trend direction (simple linear trend)
            def calculate_trend(values):
                if len(values) < 2:
                    return "stable"
                
                first_half = sum(values[:len(values)//2]) / (len(values)//2)
                second_half = sum(values[len(values)//2:]) / (len(values) - len(values)//2)
                
                if second_half > first_half * 1.1:
                    return "increasing"
                elif second_half < first_half * 0.9:
                    return "decreasing"
                else:
                    return "stable"
            
            return {
                "response_time_trend": calculate_trend(response_times),
                "request_volume_trend": calculate_trend(request_volumes),
                "error_rate_trend": calculate_trend(error_rates),
                "avg_response_time": sum(response_times) / len(response_times) if response_times else 0,
                "avg_request_volume": sum(request_volumes) / len(request_volumes) if request_volumes else 0,
                "avg_error_rate": sum(error_rates) / len(error_rates) if error_rates else 0
            }
            
        except Exception as e:
            logger.error(f"Failed to get performance trends for {config_id}: {e}")
            return {}
    
    async def generate_report(
        self, 
        config_ids: Optional[List[str]] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        format: str = "json"
    ) -> Optional[str]:
        """Generate comprehensive analytics report"""
        try:
            if not end_date:
                end_date = now_utc()
            if not start_date:
                start_date = end_date - timedelta(days=30)
            
            # Get configurations to include
            if config_ids:
                configs = []
                for config_id in config_ids:
                    config = await self.api_service.get_api_config(config_id)
                    if config:
                        configs.append(config)
            else:
                configs, _ = await self.api_service.list_api_configs(page=1, per_page=1000)
            
            # Generate report data
            report_data = {
                "report_metadata": {
                    "generated_at": now_utc(),
                    "period_start": start_date,
                    "period_end": end_date,
                    "total_apis": len(configs),
                    "format": format
                },
                "apis": []
            }
            
            for config in configs:
                api_analytics = await self.get_api_detailed_analytics(str(config.id), 
                    (end_date - start_date).days)
                report_data["apis"].append(api_analytics)
            
            # Format output
            if format.lower() == "json":
                return json.dumps(report_data, indent=2, default=str)
            else:
                raise ValueError(f"Unsupported report format: {format}")
                
        except Exception as e:
            logger.error(f"Failed to generate analytics report: {e}")
            return None
    
    def _empty_analytics(self) -> Dict[str, Any]:
        """Return empty analytics structure"""
        return {
            "overview": {
                "total_apis": 0,
                "active_apis": 0,
                "healthy_apis": 0,
                "health_percentage": 0,
                "last_updated": now_utc()
            },
            "breakdown": {
                "environments": {},
                "statuses": {},
                "auth_types": {}
            },
            "recent_metrics": {},
            "top_apis": [],
            "problematic_apis": []
        }
