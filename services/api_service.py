"""
API Configuration Service for managing API configurations with CRUD operations,
validation, encryption, and health monitoring.
"""

from __future__ import annotations

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import aiohttp
import base64
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import os

from models.api import (
    APIConfiguration,
    APIEndpoint,
    APIHealthStatus,
    APIUsageMetrics,
    APIRequestLog,
    APICredential,
    APIAuditLog,
    AuthenticationType,
    APIEnvironment,
    APIStatus,
    HTTPMethod,
)
from models.base import now_utc
from database.connection import get_collection
from config.settings import get_settings
from utils.validation import ValidationError

logger = logging.getLogger(__name__)


class EncryptionService:
    """Service for encrypting and decrypting sensitive API data"""

    def __init__(self):
        self.settings = get_settings()
        self._fernet = None
        self._init_encryption()

    def _init_encryption(self):
        """Initialize encryption with a key derived from settings"""
        # Get encryption key from environment or generate a secure default
        encryption_key = os.getenv("API_ENCRYPTION_KEY")

        if not encryption_key:
            logger.warning(
                "API_ENCRYPTION_KEY not set. Using generated key. "
                "Set API_ENCRYPTION_KEY environment variable for production use."
            )
            # Generate a secure random key for this session
            encryption_key = base64.urlsafe_b64encode(os.urandom(32)).decode()

        # Get salt from environment or generate a secure default
        salt_env = os.getenv("API_ENCRYPTION_SALT")
        if salt_env:
            try:
                salt = base64.urlsafe_b64decode(salt_env.encode())
            except Exception as e:
                logger.warning(
                    f"Invalid API_ENCRYPTION_SALT format: {e}. Using generated salt. "
                    "Ensure API_ENCRYPTION_SALT is valid base64."
                )
                # Generate a secure random salt for this session
                salt = os.urandom(16)
        else:
            logger.warning(
                "API_ENCRYPTION_SALT not set. Using generated salt. "
                "Set API_ENCRYPTION_SALT environment variable for production use."
            )
            # Generate a secure random salt for this session
            salt = os.urandom(16)

        try:
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(encryption_key.encode()))
            self._fernet = Fernet(key)
        except Exception as e:
            logger.error(f"Failed to initialize encryption: {e}")
            raise RuntimeError("Encryption initialization failed")

    def encrypt(self, data: str) -> str:
        """Encrypt sensitive data"""
        if not data:
            return ""
        return self._fernet.encrypt(data.encode()).decode()

    def decrypt(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        if not encrypted_data:
            return ""

        # Handle case where data might not be encrypted (plain text)
        if not self._is_encrypted_data(encrypted_data):
            logger.debug(f"Data appears to be plain text, returning as-is")
            return encrypted_data

        try:
            return self._fernet.decrypt(encrypted_data.encode()).decode()
        except Exception as e:
            logger.warning(f"Failed to decrypt data: {e}")
            # Return placeholder instead of raising exception
            return "[DECRYPTION_FAILED]"

    def _is_encrypted_data(self, data: str) -> bool:
        """Check if data appears to be encrypted (Fernet tokens are base64 and have specific format)"""
        if not data:
            return False

        # Fernet tokens are base64 encoded and typically much longer than plain text
        # They also don't contain common plain text patterns
        try:
            import base64

            # Try to decode as base64 - if it fails, likely plain text
            decoded = base64.urlsafe_b64decode(data + "==")  # Add padding
            # Fernet tokens have a specific minimum length
            return len(decoded) >= 32  # Fernet minimum is around 60 bytes encoded
        except:
            return False


class APIConfigurationService:
    """Service for managing API configurations"""

    def __init__(self):
        self.settings = get_settings()
        self.encryption = EncryptionService()
        self.api_configs = get_collection("api_configurations")
        self.api_endpoints = get_collection("api_endpoints")
        self.api_health = get_collection("api_health_status")
        self.api_metrics = get_collection("api_usage_metrics")
        self.api_logs = get_collection("api_request_logs")
        self.api_credentials = get_collection("api_credentials")
        self.api_audit = get_collection("api_audit_logs")

    async def create_api_config(
        self, config_data: Dict[str, Any], created_by: str
    ) -> APIConfiguration:
        """Create a new API configuration"""
        try:
            # Encrypt sensitive authentication data
            auth_config = config_data.get("authentication", {})
            if auth_config:
                auth_config = self._encrypt_auth_config(auth_config)
                config_data["authentication"] = auth_config

            # Set system fields
            config_data["created_by"] = created_by
            config_data["created_at"] = now_utc()

            # Create and validate the configuration
            api_config = APIConfiguration(**config_data)

            # Insert into database
            result = await self.api_configs.insert_one(api_config.to_mongo())
            api_config.id = result.inserted_id

            # Create initial health status
            await self._create_initial_health_status(str(api_config.id))

            # Log the creation
            await self._audit_log(
                operation="create",
                resource_type="api_config",
                resource_id=str(api_config.id),
                actor_id=created_by,
                new_values={"name": api_config.name, "base_url": api_config.base_url},
                success=True,
            )

            logger.info(
                f"Created API configuration: {api_config.name} (ID: {api_config.id})"
            )
            return api_config

        except Exception as e:
            logger.error(f"Failed to create API configuration: {e}")
            await self._audit_log(
                operation="create",
                resource_type="api_config",
                resource_id="",
                actor_id=created_by,
                new_values=config_data.get("name", "unknown"),
                success=False,
                error_message=str(e),
            )
            raise ValidationError(f"Failed to create API configuration: {e}")

    async def get_api_config(
        self, config_id: str, decrypt_sensitive: bool = False
    ) -> Optional[APIConfiguration]:
        """Get API configuration by ID"""
        try:
            # Accept both string and ObjectId identifiers
            lookup_id = config_id
            try:
                from bson import ObjectId

                if isinstance(config_id, str) and ObjectId.is_valid(config_id):
                    lookup_id = ObjectId(config_id)
            except Exception:
                pass

            doc = await self.api_configs.find_one(
                {"_id": lookup_id, "is_deleted": {"$ne": True}}
            )
            if not doc:
                return None

            api_config = APIConfiguration.from_mongo(doc)

            # Decrypt sensitive data if requested
            if decrypt_sensitive:
                api_config.authentication = self._decrypt_auth_config(
                    api_config.authentication
                )

            return api_config

        except Exception as e:
            logger.error(f"Failed to get API configuration {config_id}: {e}")
            return None

    async def list_api_configs(
        self,
        page: int = 1,
        per_page: int = 20,
        environment: Optional[APIEnvironment] = None,
        status: Optional[APIStatus] = None,
        search: Optional[str] = None,
    ) -> Tuple[List[APIConfiguration], int]:
        """List API configurations with filtering and pagination"""
        try:
            # Build filter
            filter_dict = {"is_deleted": {"$ne": True}}

            if environment:
                filter_dict["environment"] = environment.value

            if status:
                filter_dict["status"] = status.value

            if search:
                filter_dict["$or"] = [
                    {"name": {"$regex": search, "$options": "i"}},
                    {"description": {"$regex": search, "$options": "i"}},
                    {"base_url": {"$regex": search, "$options": "i"}},
                ]

            # Get total count
            total = await self.api_configs.count_documents(filter_dict)

            # Get paginated results
            skip = (page - 1) * per_page
            cursor = (
                self.api_configs.find(filter_dict)
                .sort("created_at", -1)
                .skip(skip)
                .limit(per_page)
            )
            docs = await cursor.to_list(per_page)

            configs = [APIConfiguration.from_mongo(doc) for doc in docs]

            return configs, total

        except Exception as e:
            logger.error(f"Failed to list API configurations: {e}")
            return [], 0

    async def update_api_config(
        self, config_id: str, update_data: Dict[str, Any], updated_by: str
    ) -> Optional[APIConfiguration]:
        """Update an existing API configuration"""
        try:
            # Normalize id
            lookup_id = config_id
            try:
                from bson import ObjectId

                if isinstance(config_id, str) and ObjectId.is_valid(config_id):
                    lookup_id = ObjectId(config_id)
            except Exception:
                pass
            # Get current configuration for audit
            current_config = await self.get_api_config(config_id)
            if not current_config:
                raise ValidationError("API configuration not found")

            # Encrypt sensitive authentication data if present
            if "authentication" in update_data:
                update_data["authentication"] = self._encrypt_auth_config(
                    update_data["authentication"]
                )

            # Set system fields
            update_data["last_modified_by"] = updated_by
            update_data["updated_at"] = now_utc()

            # Update in database
            result = await self.api_configs.update_one(
                {"_id": lookup_id}, {"$set": update_data}
            )

            # Support both Motor result and in-memory boolean
            modified = 0
            try:
                modified = getattr(result, "modified_count", 0)
            except Exception:
                modified = 0
            if isinstance(result, bool):
                modified = 1 if result else 0

            if modified == 0:
                raise ValidationError("No changes were made")

            # Get updated configuration
            updated_config = await self.get_api_config(config_id)

            # Log the update
            await self._audit_log(
                operation="update",
                resource_type="api_config",
                resource_id=config_id,
                actor_id=updated_by,
                old_values={"name": current_config.name},
                new_values=update_data,
                success=True,
            )

            logger.info(f"Updated API configuration: {config_id}")
            return updated_config

        except Exception as e:
            logger.error(f"Failed to update API configuration {config_id}: {e}")
            await self._audit_log(
                operation="update",
                resource_type="api_config",
                resource_id=config_id,
                actor_id=updated_by,
                success=False,
                error_message=str(e),
            )
            raise ValidationError(f"Failed to update API configuration: {e}")

    async def delete_api_config(self, config_id: str, deleted_by: str) -> bool:
        """Soft delete an API configuration"""
        try:
            # Normalize id
            lookup_id = config_id
            try:
                from bson import ObjectId

                if isinstance(config_id, str) and ObjectId.is_valid(config_id):
                    lookup_id = ObjectId(config_id)
            except Exception:
                pass
            config = await self.get_api_config(config_id)
            if not config:
                return False

            # Soft delete
            result = await self.api_configs.update_one(
                {"_id": lookup_id},
                {
                    "$set": {
                        "is_deleted": True,
                        "deleted_at": now_utc(),
                        "last_modified_by": deleted_by,
                    }
                },
            )

            # Support both Motor result and in-memory boolean
            modified = 0
            try:
                modified = getattr(result, "modified_count", 0)
            except Exception:
                modified = 0
            if isinstance(result, bool):
                modified = 1 if result else 0

            if modified > 0:
                # Log the deletion
                await self._audit_log(
                    operation="delete",
                    resource_type="api_config",
                    resource_id=config_id,
                    actor_id=deleted_by,
                    old_values={"name": config.name},
                    success=True,
                )

                logger.info(f"Deleted API configuration: {config_id}")
                return True

            return False

        except Exception as e:
            logger.error(f"Failed to delete API configuration {config_id}: {e}")
            await self._audit_log(
                operation="delete",
                resource_type="api_config",
                resource_id=config_id,
                actor_id=deleted_by,
                success=False,
                error_message=str(e),
            )
            return False

    async def test_api_connection(
        self, config_id: str, tested_by: str
    ) -> Dict[str, Any]:
        """Test API connection and return results"""
        try:
            config = await self.get_api_config(config_id, decrypt_sensitive=True)
            if not config:
                raise ValidationError("API configuration not found")

            start_time = datetime.now()

            # Prepare test request
            test_url = config.base_url
            if config.health_check.enabled and config.health_check.endpoint:
                test_url += config.health_check.endpoint

            headers = config.default_headers.copy()

            # Add authentication headers
            auth_headers = self._build_auth_headers(config.authentication)
            headers.update(auth_headers)

            # Make test request
            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=config.timeout.total_timeout)
            ) as session:
                async with session.request(
                    config.health_check.method.value,
                    test_url,
                    headers=headers,
                    params=config.default_params,
                ) as response:
                    response_time = (datetime.now() - start_time).total_seconds() * 1000
                    response_text = await response.text()

                    # Check if response meets health check criteria
                    is_healthy = (
                        response.status in config.health_check.expected_status_codes
                        and (
                            not config.health_check.expected_response_contains
                            or config.health_check.expected_response_contains
                            in response_text
                        )
                    )

                    result = {
                        "success": is_healthy,
                        "status_code": response.status,
                        "response_time_ms": int(response_time),
                        "response_size_bytes": len(response_text),
                        "headers": dict(response.headers),
                        "error_message": (
                            None
                            if is_healthy
                            else f"Unexpected status: {response.status}"
                        ),
                    }

                    # Log the test
                    await self._audit_log(
                        operation="test_connection",
                        resource_type="api_config",
                        resource_id=config_id,
                        actor_id=tested_by,
                        new_values=result,
                        success=is_healthy,
                    )

                    return result

        except Exception as e:
            error_msg = str(e)
            logger.error(f"API connection test failed for {config_id}: {error_msg}")

            result = {
                "success": False,
                "status_code": None,
                "response_time_ms": None,
                "response_size_bytes": None,
                "headers": {},
                "error_message": error_msg,
            }

            await self._audit_log(
                operation="test_connection",
                resource_type="api_config",
                resource_id=config_id,
                actor_id=tested_by,
                success=False,
                error_message=error_msg,
            )

            return result

    async def bulk_update_status(
        self, config_ids: List[str], status: APIStatus, updated_by: str
    ) -> int:
        """Bulk update status for multiple API configurations"""
        try:
            # Normalize ids for query
            lookup_ids = []
            try:
                from bson import ObjectId

                for cid in config_ids:
                    if isinstance(cid, str) and ObjectId.is_valid(cid):
                        lookup_ids.append(ObjectId(cid))
                    else:
                        lookup_ids.append(cid)
            except Exception:
                lookup_ids = config_ids

            result = await self.api_configs.update_many(
                {"_id": {"$in": lookup_ids}, "is_deleted": {"$ne": True}},
                {
                    "$set": {
                        "status": status.value,
                        "last_modified_by": updated_by,
                        "updated_at": now_utc(),
                    }
                },
            )

            # Log bulk operation
            await self._audit_log(
                operation="bulk_update_status",
                resource_type="api_config",
                resource_id=",".join(config_ids),
                actor_id=updated_by,
                new_values={"status": status.value, "count": result.modified_count},
                success=True,
            )

            logger.info(
                f"Bulk updated {result.modified_count} API configurations to status: {status.value}"
            )
            # Support both Motor result and in-memory boolean
            modified = 0
            try:
                modified = getattr(result, "modified_count", 0)
            except Exception:
                modified = 0
            if isinstance(result, bool):
                modified = 1 if result else 0
            return modified

        except Exception as e:
            logger.error(f"Failed to bulk update API configurations: {e}")
            await self._audit_log(
                operation="bulk_update_status",
                resource_type="api_config",
                resource_id=",".join(config_ids),
                actor_id=updated_by,
                success=False,
                error_message=str(e),
            )
            return 0

    def _encrypt_auth_config(self, auth_config: Dict[str, Any]) -> Dict[str, Any]:
        """Encrypt sensitive fields in authentication configuration"""
        encrypted_config = auth_config.copy()

        # Fields that need encryption
        sensitive_fields = [
            "api_key",
            "bearer_token",
            "password",
            "oauth2_client_secret",
        ]

        for field in sensitive_fields:
            if field in encrypted_config and encrypted_config[field]:
                encrypted_config[field] = self.encryption.encrypt(
                    encrypted_config[field]
                )

        return encrypted_config

    def _decrypt_auth_config(self, auth_config) -> Dict[str, Any]:
        """Decrypt sensitive fields in authentication configuration"""
        if hasattr(auth_config, "dict"):
            decrypted_config = auth_config.dict()
        else:
            decrypted_config = dict(auth_config)

        # Fields that need decryption
        sensitive_fields = [
            "api_key",
            "bearer_token",
            "password",
            "oauth2_client_secret",
        ]

        for field in sensitive_fields:
            if field in decrypted_config and decrypted_config[field]:
                # The decrypt method now handles errors gracefully
                decrypted_config[field] = self.encryption.decrypt(
                    decrypted_config[field]
                )

        return decrypted_config

    def _build_auth_headers(self, auth_config) -> Dict[str, str]:
        """Build authentication headers from configuration"""
        headers = {}

        if hasattr(auth_config, "dict"):
            auth_dict = auth_config.dict()
        else:
            auth_dict = dict(auth_config)

        auth_type = auth_dict.get("type")

        if auth_type == AuthenticationType.API_KEY:
            api_key = auth_dict.get("api_key")
            header_name = auth_dict.get("api_key_header", "X-API-Key")
            if api_key:
                headers[header_name] = api_key

        elif auth_type == AuthenticationType.BEARER_TOKEN:
            token = auth_dict.get("bearer_token")
            if token:
                headers["Authorization"] = f"Bearer {token}"

        elif auth_type == AuthenticationType.BASIC_AUTH:
            username = auth_dict.get("username")
            password = auth_dict.get("password")
            if username and password:
                credentials = base64.b64encode(
                    f"{username}:{password}".encode()
                ).decode()
                headers["Authorization"] = f"Basic {credentials}"

        elif auth_type == AuthenticationType.CUSTOM_HEADER:
            custom_headers = auth_dict.get("custom_headers", {})
            headers.update(custom_headers)

        return headers

    async def _create_initial_health_status(self, config_id: str) -> None:
        """Create initial health status for a new API configuration"""
        try:
            health_status = APIHealthStatus(
                api_config_id=config_id,
                is_healthy=True,
                last_check_at=now_utc(),
                next_check_at=now_utc() + timedelta(minutes=5),
                consecutive_failures=0,
                consecutive_successes=0,
                total_checks=0,
                total_failures=0,
                uptime_percentage=100.0,
            )

            await self.api_health.insert_one(health_status.to_mongo())
            logger.debug(f"Created initial health status for API config: {config_id}")

        except Exception as e:
            logger.error(f"Failed to create initial health status for {config_id}: {e}")

    async def _audit_log(
        self,
        operation: str,
        resource_type: str,
        resource_id: str,
        actor_id: str,
        old_values: Optional[Dict[str, Any]] = None,
        new_values: Optional[Dict[str, Any]] = None,
        success: bool = True,
        error_message: Optional[str] = None,
    ) -> None:
        """Create an audit log entry"""
        try:
            audit_log = APIAuditLog(
                operation=operation,
                resource_type=resource_type,
                resource_id=resource_id,
                actor_id=actor_id,
                old_values=old_values or {},
                new_values=new_values or {},
                success=success,
                error_message=error_message,
                timestamp=now_utc(),
            )

            await self.api_audit.insert_one(audit_log.to_mongo())

        except Exception as e:
            logger.error(f"Failed to create audit log: {e}")

    async def get_api_health_status(self, config_id: str) -> Optional[APIHealthStatus]:
        """Get current health status for an API configuration"""
        try:
            doc = await self.api_health.find_one({"api_config_id": config_id})
            if not doc:
                return None

            return APIHealthStatus.from_mongo(doc)

        except Exception as e:
            logger.error(f"Failed to get health status for {config_id}: {e}")
            return None

    async def get_usage_metrics(
        self, config_id: str, period_type: str = "daily", days: int = 7
    ) -> List[APIUsageMetrics]:
        """Get usage metrics for an API configuration"""
        try:
            end_date = now_utc()
            start_date = end_date - timedelta(days=days)

            cursor = self.api_metrics.find(
                {
                    "api_config_id": config_id,
                    "period_type": period_type,
                    "period_start": {"$gte": start_date, "$lte": end_date},
                }
            ).sort("period_start", -1)

            docs = await cursor.to_list(None)
            return [APIUsageMetrics.from_mongo(doc) for doc in docs]

        except Exception as e:
            logger.error(f"Failed to get usage metrics for {config_id}: {e}")
            return []

    async def export_api_configs(self, format: str = "json") -> Optional[str]:
        """Export all API configurations (without sensitive data)"""
        try:
            configs, _ = await self.list_api_configs(page=1, per_page=1000)

            export_data = []
            for config in configs:
                config_dict = config.dict()

                # Remove sensitive authentication data
                if "authentication" in config_dict:
                    auth = config_dict["authentication"]
                    # Keep structure but remove sensitive values
                    for sensitive_field in [
                        "api_key",
                        "bearer_token",
                        "password",
                        "oauth2_client_secret",
                    ]:
                        if sensitive_field in auth:
                            auth[sensitive_field] = "[REDACTED]"

                # Remove system fields
                for field in ["id", "created_at", "updated_at", "deleted_at"]:
                    config_dict.pop(field, None)

                export_data.append(config_dict)

            if format.lower() == "json":
                return json.dumps(export_data, indent=2, default=str)
            else:
                raise ValueError(f"Unsupported export format: {format}")

        except Exception as e:
            logger.error(f"Failed to export API configurations: {e}")
            return None
