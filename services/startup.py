"""
Startup service for initializing background services
"""

import asyncio
import logging
from typing import Optional

logger = logging.getLogger(__name__)


class StartupService:
    """Service for managing application startup and background tasks"""
    
    def __init__(self):
        self._checkout_queue_task: Optional[asyncio.Task] = None
        self._is_running = False
        
    async def start_background_services(self) -> None:
        """Start all background services"""
        try:
            logger.info("Starting background services...")
            
            # Start checkout queue processing
            await self._start_checkout_queue()
            
            self._is_running = True
            logger.info("All background services started successfully")
            
        except Exception as e:
            logger.error(f"Error starting background services: {e}")
            raise
            
    async def stop_background_services(self) -> None:
        """Stop all background services"""
        try:
            logger.info("Stopping background services...")
            
            self._is_running = False
            
            # Stop checkout queue processing
            await self._stop_checkout_queue()
            
            logger.info("All background services stopped")
            
        except Exception as e:
            logger.error(f"Error stopping background services: {e}")
            
    async def _start_checkout_queue(self) -> None:
        """Start the checkout queue processing service"""
        try:
            from services.checkout_queue_service import CheckoutQueueService
            
            queue_service = CheckoutQueueService()
            await queue_service.start_worker()
            
            logger.info("Checkout queue service started")
            
        except Exception as e:
            logger.error(f"Error starting checkout queue service: {e}")
            raise
            
    async def _stop_checkout_queue(self) -> None:
        """Stop the checkout queue processing service"""
        try:
            from services.checkout_queue_service import CheckoutQueueService
            
            queue_service = CheckoutQueueService()
            await queue_service.stop_worker()
            
            logger.info("Checkout queue service stopped")
            
        except Exception as e:
            logger.error(f"Error stopping checkout queue service: {e}")
            
    @property
    def is_running(self) -> bool:
        """Check if background services are running"""
        return self._is_running


# Global startup service instance
_startup_service: Optional[StartupService] = None


def get_startup_service() -> StartupService:
    """Get the global startup service instance"""
    global _startup_service
    if _startup_service is None:
        _startup_service = StartupService()
    return _startup_service


async def initialize_services() -> None:
    """Initialize all background services"""
    startup_service = get_startup_service()
    await startup_service.start_background_services()


async def shutdown_services() -> None:
    """Shutdown all background services"""
    startup_service = get_startup_service()
    await startup_service.stop_background_services()
