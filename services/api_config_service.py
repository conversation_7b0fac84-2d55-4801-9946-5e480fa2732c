"""
Centralized API Configuration Management Service
Manages all external API endpoints, credentials, and settings
"""

from __future__ import annotations

import asyncio
import json
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, asdict
from enum import Enum
import aiohttp
from cryptography.fernet import Fernet
import base64

from database.connection import get_collection
from models.base import now_utc
from services.api_service import (
    APIConfigurationService as PydanticAPIConfigService,
)
from models.api import AuthenticationType, HTTPMethod, APIStatus
from config.settings import get_settings

logger = logging.getLogger(__name__)


# Import auth profile service (avoid circular import)
def get_auth_profile_service():
    from services.auth_profile_service import get_auth_profile_service

    return get_auth_profile_service()


class ConfigSource(str, Enum):
    """Configuration source priority"""

    ADMIN_PANEL = "admin_panel"
    DATABASE = "database"
    ENVIRONMENT = "environment"
    DEFAULT = "default"


@dataclass
class APIEndpoint:
    """API endpoint configuration"""

    name: str
    url: str
    method: str = "GET"
    timeout: int = 30
    retry_count: int = 3
    retry_delays: List[int] = None

    def __post_init__(self):
        if self.retry_delays is None:
            self.retry_delays = [1, 2, 4]


@dataclass
class APICredentials:
    """API authentication credentials"""

    login_token: str = ""
    session_cookies: Dict[str, str] = None
    headers: Dict[str, str] = None
    # New fields for profile-based authentication
    auth_profile_id: Optional[str] = None
    use_profile: bool = False
    credential_overrides: Dict[str, Any] = None

    def __post_init__(self):
        if self.session_cookies is None:
            self.session_cookies = {}
        if self.headers is None:
            self.headers = {}
        if self.credential_overrides is None:
            self.credential_overrides = {}


@dataclass
class APIConfiguration:
    """Complete API configuration"""

    service_name: str
    base_url: str
    endpoints: Dict[str, APIEndpoint]
    credentials: APICredentials
    enabled: bool = True
    last_updated: datetime = None
    source: ConfigSource = ConfigSource.DEFAULT
    # Enhanced fields for better organization
    display_name: str = ""
    description: str = ""
    category: str = "general"
    tags: List[str] = None
    environment: str = "development"
    version: str = "1.0"
    health_check_endpoint: str = ""
    documentation_url: str = ""

    def __post_init__(self):
        if self.last_updated is None:
            self.last_updated = datetime.now(timezone.utc)
        if self.tags is None:
            self.tags = []
        if not self.display_name:
            self.display_name = self.service_name.replace("_", " ").title()


class APIConfigService:
    """Centralized API configuration management service"""

    def __init__(self):
        self.settings = get_settings()
        # Underlying Pydantic-backed service (single source of truth)
        self._delegate = PydanticAPIConfigService()
        # Collections used by the delegate; keep handles for direct queries when needed
        self.config_collection = get_collection("api_configurations")
        self.audit_collection = get_collection("api_audit_logs")
        self.endpoints_collection = get_collection("api_endpoints")

        # Configuration cache
        self._config_cache: Dict[str, APIConfiguration] = {}
        self._cache_expiry: Dict[str, datetime] = {}
        self._cache_ttl = timedelta(minutes=5)

        # Encryption for sensitive data
        # Initialize encryption. Expect a standard Fernet key (URL-safe base64, 32 bytes).
        self._encryption_key = self._get_encryption_key()
        self._cipher = Fernet(self._encryption_key)

        # Configuration lock for thread safety
        self._config_lock = asyncio.Lock()

    def _get_encryption_key(self) -> bytes:
        """Get or generate encryption key for sensitive data.

        Expects `API_CONFIG_ENCRYPTION_KEY` to be a valid Fernet key
        (URL-safe base64-encoded 32-byte key). If invalid or unset,
        generates a new ephemeral key for this process.
        """
        key = getattr(self.settings, "API_CONFIG_ENCRYPTION_KEY", None)
        if key:
            # Accept provided key as-is (bytes for Fernet), validating format.
            key_bytes = key.encode() if isinstance(key, str) else key
            try:
                # Validate by attempting to construct a Fernet instance
                Fernet(key_bytes)
                return key_bytes
            except Exception:
                logger.warning(
                    "Invalid API_CONFIG_ENCRYPTION_KEY provided; generating a temporary key for this run."
                )
        # Generate new key (store securely in production)
        return Fernet.generate_key()

    def _encrypt_sensitive_data(self, data: str) -> str:
        """Encrypt sensitive configuration data"""
        if not data:
            return ""
        return self._cipher.encrypt(data.encode()).decode()

    def _decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive configuration data"""
        if not encrypted_data:
            return ""
        try:
            return self._cipher.decrypt(encrypted_data.encode()).decode()
        except Exception as e:
            # Often occurs if API_CONFIG_ENCRYPTION_KEY changed between runs.
            logger.warning(
                "Failed to decrypt sensitive data. Set stable API_CONFIG_ENCRYPTION_KEY to preserve tokens."
            )
            return ""

    async def get_api_config(self, service_name: str) -> Optional[APIConfiguration]:
        """Get API configuration by name using the Pydantic-backed service.

        Keeps the dataclass return type for admin UI compatibility.
        """
        async with self._config_lock:
            # Cache
            if service_name in self._config_cache:
                cache_time = self._cache_expiry.get(service_name)
                if cache_time and datetime.now(timezone.utc) < cache_time:
                    return self._config_cache[service_name]

            # Find matching config by name
            p_configs, _ = await self._delegate.list_api_configs(
                page=1, per_page=50, search=service_name
            )
            p_match = next(
                (
                    c
                    for c in p_configs
                    if (c.name or "").lower() == service_name.lower()
                ),
                None,
            )

            if p_match is None:
                # Fallback to environment/defaults for well-known services
                config = (
                    self._get_external_cart_env_config()
                    if service_name == "external_cart"
                    else None
                )
            else:
                # Get decrypted config and build dataclass adapter
                p_dec = await self._delegate.get_api_config(
                    str(p_match.id), decrypt_sensitive=True
                )
                config = await self._build_dataclass_config_from_pydantic(p_dec)

            if config:
                self._config_cache[service_name] = config
                self._cache_expiry[service_name] = (
                    datetime.now(timezone.utc) + self._cache_ttl
                )
            return config

    async def _load_configuration(
        self, service_name: str
    ) -> Optional[APIConfiguration]:
        """Load configuration from multiple sources with priority"""
        try:
            # Priority 1: Admin panel settings (database with admin_panel source)
            admin_config = await self._load_from_database(
                service_name, ConfigSource.ADMIN_PANEL
            )
            if admin_config:
                return admin_config

            # Priority 2: Database settings
            db_config = await self._load_from_database(
                service_name, ConfigSource.DATABASE
            )
            if db_config:
                return db_config

            # Priority 3: Environment variables
            env_config = self._load_from_environment(service_name)
            if env_config:
                return env_config

            # Priority 4: Default configuration
            return self._get_default_configuration(service_name)

        except Exception as e:
            logger.error(f"Error loading configuration for {service_name}: {e}")
            return self._get_default_configuration(service_name)

    async def _load_from_database(
        self, service_name: str, source: ConfigSource
    ) -> Optional[APIConfiguration]:
        """Load configuration from database"""
        try:
            doc = await self.config_collection.find_one(
                {"service_name": service_name, "source": source.value, "enabled": True}
            )

            if not doc:
                return None

            # Decrypt sensitive data
            credentials_data = doc.get("credentials", {})
            if credentials_data.get("login_token_encrypted"):
                credentials_data["login_token"] = self._decrypt_sensitive_data(
                    credentials_data["login_token_encrypted"]
                )
                # Remove encrypted field to avoid conflicts
                credentials_data.pop("login_token_encrypted", None)

            # Convert to APIConfiguration
            config = APIConfiguration(
                service_name=doc["service_name"],
                base_url=doc["base_url"],
                endpoints={
                    name: APIEndpoint(**endpoint_data)
                    for name, endpoint_data in doc.get("endpoints", {}).items()
                },
                credentials=APICredentials(**credentials_data),
                enabled=doc.get("enabled", True),
                last_updated=doc.get("last_updated", datetime.now(timezone.utc)),
                source=ConfigSource(doc.get("source", ConfigSource.DATABASE.value)),
            )

            return config

        except Exception as e:
            logger.error(f"Error loading database config for {service_name}: {e}")
            return None

    def _load_from_environment(self, service_name: str) -> Optional[APIConfiguration]:
        """Load configuration from environment variables"""
        try:
            if service_name == "api1_external_cart":
                return self._get_external_cart_env_config()
            # Add other services as needed
            return None
        except Exception as e:
            logger.error(f"Error loading environment config for {service_name}: {e}")
            return None

    def _get_external_cart_env_config(self) -> Optional[APIConfiguration]:
        """Get external cart configuration from environment variables"""
        base_url = "https://ronaldo-club.to/api"

        # Updated credentials based on demo examples
        credentials = APICredentials(
            login_token=getattr(self.settings, "EXTERNAL_LOGIN_TOKEN", ""),
            session_cookies={
                "__ddg1_": getattr(
                    self.settings, "EXTERNAL_DDG1", "u1UaBqLkngSC4ZTJRDQC"
                ),
                "__ddg8_": getattr(self.settings, "EXTERNAL_DDG8", ""),
                "__ddg9_": getattr(self.settings, "EXTERNAL_DDG9", ""),
                "__ddg10_": getattr(self.settings, "EXTERNAL_DDG10", ""),
                "_ga": getattr(self.settings, "EXTERNAL_GA", ""),
                "_ga_KZWCRF57VT": getattr(self.settings, "EXTERNAL_GA_KZWCRF57VT", ""),
                "testcookie": "1",
            },
            headers={
                "accept": "application/json, text/plain, */*",
                "accept-language": "en-US,en;q=0.9",
                "origin": "https://ronaldo-club.to",
                "priority": "u=1, i",
                "sec-ch-ua": '"Chromium";v="140", "Not=A?Brand";v="24", "Brave";v="140"',
                "sec-ch-ua-mobile": "?1",
                "sec-ch-ua-platform": '"Android"',
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "sec-gpc": "1",
                "user-agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) "
                "AppleWebKit/537.36 (KHTML, like Gecko) "
                "Chrome/140.0.0.0 Mobile Safari/537.36",
            },
        )

        # Updated endpoints based on demo examples
        endpoints = {
            "list_items": APIEndpoint(
                "list_items", f"{base_url}/cards/hq/list", "POST", 30
            ),
            "cart_view": APIEndpoint("cart_view", f"{base_url}/cart/", "GET", 30),
            "cart_add": APIEndpoint("cart_add", f"{base_url}/cart/", "POST", 30),
            "cart_remove": APIEndpoint(
                "cart_remove", f"{base_url}/cart/", "DELETE", 30
            ),
            "user_info": APIEndpoint("user_info", f"{base_url}/user/getme", "GET", 30),
            # Updated to match current external API: GET /cart/checkout
            "checkout": APIEndpoint(
                "checkout", f"{base_url}/cart/checkout", "GET", 60
            ),
        }

        return APIConfiguration(
            service_name="api1_external_cart",
            base_url=base_url,
            endpoints=endpoints,
            credentials=credentials,
            source=ConfigSource.ENVIRONMENT,
            display_name="API 1 - External Cart API",
            description="API 1: External API for cart operations, item listing, and user management (Ronaldo Club)",
            category="ecommerce",
            tags=["api1", "cart", "external", "api", "ecommerce", "ronaldo"],
            environment="production",
            version="1.0",
            health_check_endpoint="/user/getme",
            documentation_url="https://ronaldo-club.to/api/docs",
        )

    def _get_default_configuration(
        self, service_name: str
    ) -> Optional[APIConfiguration]:
        """Get default configuration for a service"""
        defaults = {
            "api1_external_cart": APIConfiguration(
                service_name="api1_external_cart",
                base_url="https://ronaldo-club.to/api",
                endpoints={
                    "list_items": APIEndpoint(
                        "list_items",
                        "https://ronaldo-club.to/api/cards/hq/list",
                        "POST",
                        30,
                    ),
                    "cart_view": APIEndpoint(
                        "cart_view", "https://ronaldo-club.to/api/cart/", "GET", 30
                    ),
                    "cart_add": APIEndpoint(
                        "cart_add", "https://ronaldo-club.to/api/cart/", "POST", 30
                    ),
                    "cart_remove": APIEndpoint(
                        "cart_remove", "https://ronaldo-club.to/api/cart/", "DELETE", 30
                    ),
                    "user_info": APIEndpoint(
                        "user_info", "https://ronaldo-club.to/api/user/getme", "GET", 30
                    ),
                    # Updated to match current external API: GET /cart/checkout
                    "checkout": APIEndpoint(
                        "checkout",
                        "https://ronaldo-club.to/api/cart/checkout",
                        "GET",
                        60,
                    ),
                },
                credentials=APICredentials(),
                source=ConfigSource.DEFAULT,
                display_name="API 1 - External Cart API",
                description="API 1: External API for cart operations, item listing, and user management (Ronaldo Club)",
                category="ecommerce",
                tags=["api1", "cart", "external", "api", "ecommerce", "ronaldo"],
                environment="development",
                version="1.0",
                health_check_endpoint="/user/getme",
                documentation_url="https://ronaldo-club.to/api/docs",
            )
        }
        return defaults.get(service_name)

    async def save_api_config(
        self,
        config: APIConfiguration,
        source: ConfigSource = ConfigSource.ADMIN_PANEL,
        user_id: str = None,
    ) -> bool:
        """Persist configuration via the Pydantic-backed service."""
        try:
            async with self._config_lock:
                # Lookup existing by name
                p_configs, _ = await self._delegate.list_api_configs(
                    page=1, per_page=50, search=config.service_name
                )
                p_match = next(
                    (
                        c
                        for c in p_configs
                        if (c.name or "").lower() == config.service_name.lower()
                    ),
                    None,
                )

                # Build auth config
                auth_config: dict[str, Any] = {"type": AuthenticationType.NONE}
                if config.credentials and config.credentials.login_token:
                    auth_config = {
                        "type": AuthenticationType.BEARER_TOKEN,
                        "bearer_token": config.credentials.login_token,
                    }

                status = APIStatus.ACTIVE if config.enabled else APIStatus.INACTIVE

                if p_match is None:
                    # Create new config
                    config_data: Dict[str, Any] = {
                        "name": config.service_name,
                        "base_url": config.base_url,
                        "authentication": auth_config,
                        "default_headers": (
                            config.credentials.headers if config.credentials else {}
                        ),
                        "status": status,
                        "tags": [],
                    }
                    created = await self._delegate.create_api_config(
                        config_data, created_by=user_id or "admin_ui"
                    )
                    cfg_id = str(created.id)
                else:
                    # Update existing
                    cfg_id = str(p_match.id)
                    update_data: Dict[str, Any] = {
                        "base_url": config.base_url,
                        "default_headers": (
                            config.credentials.headers if config.credentials else {}
                        ),
                        "status": status,
                    }
                    # Only include authentication if token changed/provided
                    if (
                        config.credentials
                        and config.credentials.login_token is not None
                    ):
                        update_data["authentication"] = auth_config
                    await self._delegate.update_api_config(
                        cfg_id, update_data, updated_by=user_id or "admin_ui"
                    )

                # Record source on the raw document for compatibility with admin UI expectations
                try:
                    # Prefer updating by name (stable across engines)
                    updated = False
                    try:
                        res = await self.config_collection.update_one(
                            {"name": config.service_name},
                            {"$set": {"source": source.value}},
                        )
                        updated = bool(
                            getattr(res, "modified_count", 0)
                            or isinstance(res, bool)
                            and res
                        )
                    except Exception:
                        updated = False
                    if not updated:
                        # Try by _id as string or ObjectId
                        from bson import ObjectId

                        obj_id = None
                        try:
                            obj_id = ObjectId(cfg_id)
                        except Exception:
                            obj_id = None
                        filters = [{"_id": cfg_id}]
                        if obj_id is not None:
                            filters.append({"_id": obj_id})
                        for flt in filters:
                            try:
                                res2 = await self.config_collection.update_one(
                                    flt, {"$set": {"source": source.value}}
                                )
                                updated = bool(
                                    getattr(res2, "modified_count", 0)
                                    or isinstance(res2, bool)
                                    and res2
                                )
                                if updated:
                                    break
                            except Exception:
                                continue
                except Exception:
                    pass

                # Persist endpoints mapping (best-effort)
                try:
                    for name, ep in (config.endpoints or {}).items():
                        # Derive path from URL
                        path = ep.url
                        try:
                            if path.startswith(config.base_url):
                                path = path[len(config.base_url) :]
                        except Exception:
                            pass
                        doc = {
                            "api_config_id": cfg_id,
                            "name": name,
                            "path": path if path.startswith("/") else f"/{path}",
                            "method": (ep.method or "GET"),
                            "updated_at": datetime.now(timezone.utc),
                        }
                        # Upsert by (config_id, name)
                        try:
                            await self.endpoints_collection.update_one(
                                {"api_config_id": cfg_id, "name": name},
                                {"$set": doc},
                                upsert=True,
                            )
                        except TypeError:
                            # In-memory fallback without upsert support
                            flt = {"api_config_id": cfg_id, "name": name}
                            try:
                                existing = await self.endpoints_collection.find_one(flt)
                            except Exception:
                                existing = None
                            if existing:
                                await self.endpoints_collection.update_one(
                                    flt, {"$set": doc}
                                )
                            else:
                                await self.endpoints_collection.insert_one({**doc})
                except Exception:
                    # Endpoints are optional; ignore errors
                    pass

                # Invalidate local cache
                self._config_cache.pop(config.service_name, None)
                self._cache_expiry.pop(config.service_name, None)
                await self._log_config_change(
                    config.service_name, source, user_id, "save"
                )
                logger.info(
                    f"Saved API configuration for {config.service_name} via Pydantic service"
                )
                return True
        except Exception as e:
            logger.error(
                f"Error saving API configuration for {config.service_name}: {e}"
            )
            return False

    async def test_api_connection(self, service_name: str) -> Dict[str, Any]:
        """Test API connection through the Pydantic-backed service."""
        try:
            # Resolve config by name
            p_configs, _ = await self._delegate.list_api_configs(
                page=1, per_page=50, search=service_name
            )
            p_match = next(
                (
                    c
                    for c in p_configs
                    if (c.name or "").lower() == service_name.lower()
                ),
                None,
            )
            if not p_match:
                return {"success": False, "message": "Configuration not found"}
            res = await self._delegate.test_api_connection(
                str(p_match.id), tested_by="admin_ui"
            )
            # Normalize keys for legacy callers
            if "message" not in res:
                msg = None
                if res.get("success"):
                    msg = "Connection successful"
                else:
                    msg = res.get("error_message") or (
                        f"HTTP {res.get('status_code')}"
                        if res.get("status_code") is not None
                        else "Connection failed"
                    )
                res["message"] = msg
            return res
        except Exception as e:
            return {"success": False, "message": f"Connection failed: {str(e)}"}

    async def _log_config_change(
        self,
        service_name: str,
        source: ConfigSource,
        user_id: str = None,
        action: str = "update",
    ) -> None:
        """Log configuration changes into unified audit collection."""
        try:
            doc = {
                "operation": action,
                "resource_type": "api_config",
                "resource_id": service_name,
                "actor_id": user_id or "admin_ui",
                "timestamp": now_utc(),
                "success": True,
            }
            await self.audit_collection.insert_one(doc)
        except Exception as e:
            logger.error(f"Error logging config change audit: {e}")

    async def get_audit_trail(
        self, service_name: str = None, limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get configuration change audit trail from unified audit logs."""
        try:
            query: Dict[str, Any] = {"resource_type": "api_config"}
            if service_name:
                # Try to resolve config id by name
                try:
                    p_configs, _ = await self._delegate.list_api_configs(
                        page=1, per_page=50, search=service_name
                    )
                    p_match = next(
                        (
                            c
                            for c in p_configs
                            if (c.name or "").lower() == service_name.lower()
                        ),
                        None,
                    )
                    if p_match:
                        query["resource_id"] = str(p_match.id)
                except Exception:
                    pass
            cursor = (
                self.audit_collection.find(query).sort("timestamp", -1).limit(limit)
            )
            docs = await cursor.to_list(None)
            # Normalize fields for admin UI
            normalized = []
            for d in docs:
                normalized.append(
                    {
                        "timestamp": d.get("timestamp"),
                        "service_name": d.get("resource_id", "unknown"),
                        "action": d.get("operation", d.get("action", "update")),
                        "source": d.get("source", "database"),
                        "user_id": d.get("actor_id"),
                    }
                )
            return normalized
        except Exception as e:
            logger.error(f"Error getting audit trail: {e}")
            return []

    async def invalidate_cache(self, service_name: str = None) -> None:
        """Invalidate configuration cache"""
        async with self._config_lock:
            if service_name:
                self._config_cache.pop(service_name, None)
                self._cache_expiry.pop(service_name, None)
            else:
                self._config_cache.clear()
                self._cache_expiry.clear()

        logger.info(f"Invalidated cache for {service_name or 'all services'}")

    async def get_all_configurations(self) -> Dict[str, APIConfiguration]:
        """Get all API configurations via Pydantic-backed service."""
        try:
            configs: Dict[str, APIConfiguration] = {}
            p_list, _ = await self._delegate.list_api_configs(page=1, per_page=200)
            for p in p_list:
                try:
                    p_dec = await self._delegate.get_api_config(
                        str(p.id), decrypt_sensitive=True
                    )
                    dc = await self._build_dataclass_config_from_pydantic(p_dec)
                    if dc:
                        configs[dc.service_name] = dc
                except Exception:
                    continue

            # Ensure well-known api1_external_cart appears even if not stored yet
            if "api1_external_cart" not in configs:
                ext = self._get_external_cart_env_config()
                if ext:
                    configs["api1_external_cart"] = ext
            return configs
        except Exception as e:
            logger.error(f"Error getting all configurations: {e}")
            return {}

    async def create_http_session(
        self, service_name: str
    ) -> Optional[aiohttp.ClientSession]:
        """Create HTTP session using dataclass-adapted configuration."""
        try:
            config = await self.get_api_config(service_name)
            if not config:
                logger.error(f"No configuration found for service {service_name}")
                return None
            timeout = aiohttp.ClientTimeout(total=60, connect=10)
            connector = aiohttp.TCPConnector(limit=10, limit_per_host=5)
            return aiohttp.ClientSession(
                timeout=timeout,
                connector=connector,
                headers=(config.credentials.headers if config.credentials else {}),
                cookies=(
                    config.credentials.session_cookies if config.credentials else {}
                ),
            )
        except Exception as e:
            logger.error(f"Error creating HTTP session for {service_name}: {e}")
            return None

    async def _build_dataclass_config_from_pydantic(
        self, p_conf
    ) -> Optional[APIConfiguration]:
        """Map Pydantic APIConfiguration to dataclass APIConfiguration for UI."""
        if not p_conf:
            return None

        # Load endpoints (best-effort)
        endpoints: Dict[str, APIEndpoint] = {}
        try:
            cursor = self.endpoints_collection.find({"api_config_id": str(p_conf.id)})
            items = await cursor.to_list(None)
            for ep in items:
                name = ep.get("name") or (ep.get("path") or "/").lstrip("/")
                path = ep.get("path") or "/"
                method = ep.get("method") or "GET"
                url = f"{p_conf.base_url}{path if path.startswith('/') else f'/{path}'}"
                endpoints[name] = APIEndpoint(
                    name=name, url=url, method=method, timeout=30
                )
        except Exception:
            pass

        # Determine source (stored alongside document as compatibility field)
        src = ConfigSource.DATABASE
        try:
            raw = await self.config_collection.find_one(
                {"_id": getattr(p_conf, "id", None)}
            )
            if raw and raw.get("source") in {s.value for s in ConfigSource}:
                src = ConfigSource(raw.get("source"))
        except Exception:
            pass

        # Map authentication to a simple bearer token if present
        token = None
        try:
            auth = getattr(p_conf, "authentication", None)
            if auth and getattr(auth, "type", None) == AuthenticationType.BEARER_TOKEN:
                token = getattr(auth, "bearer_token", None)
        except Exception:
            token = None

        creds = APICredentials(
            login_token=token or "",
            session_cookies={},
            headers=getattr(p_conf, "default_headers", {}) or {},
        )

        return APIConfiguration(
            service_name=getattr(p_conf, "name", "unknown"),
            base_url=getattr(p_conf, "base_url", ""),
            endpoints=endpoints,
            credentials=creds,
            enabled=(getattr(p_conf, "status", APIStatus.ACTIVE) != APIStatus.INACTIVE),
            source=src,
        )

    async def delete_api_config(self, service_name: str, user_id: str = "") -> bool:
        """Delete an API configuration"""
        try:
            async with self._config_lock:
                # Find existing configuration
                p_configs, _ = await self._delegate.list_api_configs(
                    page=1, per_page=50, search=service_name
                )
                p_match = next(
                    (
                        c
                        for c in p_configs
                        if (c.name or "").lower() == service_name.lower()
                    ),
                    None,
                )

                if not p_match:
                    return False

                # Delete via delegate
                success = await self._delegate.delete_api_config(
                    str(p_match.id), user_id
                )

                if success:
                    # Clear from cache
                    self._config_cache.pop(service_name, None)
                    self._cache_expiry.pop(service_name, None)

                return success

        except Exception as e:
            logger.error(f"Error deleting API config {service_name}: {e}")
            return False

    # Authentication Profile Integration Methods

    async def assign_auth_profile(
        self,
        service_name: str,
        auth_profile_id: str,
        user_id: str,
        credential_overrides: Optional[Dict[str, Any]] = None,
        header_overrides: Optional[Dict[str, str]] = None,
    ) -> bool:
        """Assign an authentication profile to an API configuration"""
        try:
            # Get the API configuration
            config = await self.get_api_config(service_name)
            if not config:
                logger.error(f"API configuration {service_name} not found")
                return False

            # Get auth profile service
            auth_service = get_auth_profile_service()

            # Verify profile exists and is active
            profile = await auth_service.get_profile_by_id(auth_profile_id)
            if not profile or not profile.is_active():
                logger.error(
                    f"Authentication profile {auth_profile_id} not found or inactive"
                )
                return False

            # Update the API configuration to use the profile
            config.credentials.auth_profile_id = auth_profile_id
            config.credentials.use_profile = True
            config.credentials.credential_overrides = credential_overrides or {}

            # Merge header overrides
            if header_overrides:
                config.credentials.headers.update(header_overrides)

            # Save the updated configuration
            success = await self.save_api_config(
                config, ConfigSource.ADMIN_PANEL, user_id
            )

            if success:
                # Create assignment record in auth profile service
                await auth_service.assign_profile_to_api(
                    api_config_id=service_name,  # Using service name as ID for now
                    auth_profile_id=auth_profile_id,
                    assigned_by=user_id,
                    override_credentials=credential_overrides,
                    override_headers=header_overrides,
                )

                logger.info(
                    f"Assigned auth profile {auth_profile_id} to API {service_name}"
                )
                return True

            return False

        except Exception as e:
            logger.error(f"Failed to assign auth profile to {service_name}: {e}")
            return False

    async def unassign_auth_profile(self, service_name: str, user_id: str) -> bool:
        """Remove authentication profile assignment from API configuration"""
        try:
            # Get the API configuration
            config = await self.get_api_config(service_name)
            if not config:
                logger.error(f"API configuration {service_name} not found")
                return False

            # Clear profile assignment
            config.credentials.auth_profile_id = None
            config.credentials.use_profile = False
            config.credentials.credential_overrides = {}

            # Save the updated configuration
            success = await self.save_api_config(
                config, ConfigSource.ADMIN_PANEL, user_id
            )

            if success:
                # Remove assignment record from auth profile service
                auth_service = get_auth_profile_service()
                await auth_service.unassign_profile_from_api(service_name)

                logger.info(f"Unassigned auth profile from API {service_name}")
                return True

            return False

        except Exception as e:
            logger.error(f"Failed to unassign auth profile from {service_name}: {e}")
            return False

    async def get_effective_credentials(
        self, service_name: str
    ) -> Optional[APICredentials]:
        """Get effective credentials for API configuration, merging profile and overrides"""
        try:
            config = await self.get_api_config(service_name)
            if not config:
                return None

            # If not using profile, return existing credentials
            if (
                not config.credentials.use_profile
                or not config.credentials.auth_profile_id
            ):
                return config.credentials

            # Get auth profile service and profile
            auth_service = get_auth_profile_service()
            profile_credentials = await auth_service.get_decrypted_credentials(
                config.credentials.auth_profile_id
            )

            if not profile_credentials:
                logger.warning(
                    f"Could not get profile credentials for {service_name}, falling back to local credentials"
                )
                return config.credentials

            # Create merged credentials
            merged_credentials = APICredentials(
                auth_profile_id=config.credentials.auth_profile_id, use_profile=True
            )

            # Merge profile credentials with local overrides
            merged_credentials.login_token = (
                config.credentials.credential_overrides.get("login_token")
                or profile_credentials.get("login_token")
                or profile_credentials.get("bearer_token")
                or config.credentials.login_token
            )

            # Merge session cookies
            merged_credentials.session_cookies = {
                **profile_credentials.get("session_cookies", {}),
                **config.credentials.session_cookies,
                **config.credentials.credential_overrides.get("session_cookies", {}),
            }

            # Merge headers
            merged_credentials.headers = {
                **profile_credentials.get("headers", {}),
                **config.credentials.headers,
                **config.credentials.credential_overrides.get("headers", {}),
            }

            # Store overrides for reference
            merged_credentials.credential_overrides = (
                config.credentials.credential_overrides
            )

            return merged_credentials

        except Exception as e:
            logger.error(f"Failed to get effective credentials for {service_name}: {e}")
            return config.credentials if config else None


# Global instance
_api_config_service: Optional[APIConfigService] = None


def get_api_config_service() -> APIConfigService:
    """Get global API configuration service instance"""
    global _api_config_service
    if _api_config_service is None:
        _api_config_service = APIConfigService()
    return _api_config_service
