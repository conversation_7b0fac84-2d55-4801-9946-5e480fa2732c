"""
Authentication Profile Service
Centralized management of reusable authentication profiles for API configurations
"""

from __future__ import annotations

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from dataclasses import asdict

from database.connection import get_collection
from models.auth_profile import (
    AuthenticationProfile,
    AuthProfileAssignment,
    AuthProfileUsageLog,
    AuthProfileTemplate,
    AuthProfileScope,
    AuthProfileStatus,
    AuthProfileCredentials,
)
from models.api import AuthenticationType, APIEnvironment
from services.api_service import EncryptionService
from config.settings import get_settings

logger = logging.getLogger(__name__)


class AuthProfileService:
    """Service for managing authentication profiles"""

    def __init__(self):
        self.settings = get_settings()
        self.encryption = EncryptionService()

        # Database collections
        self.profiles_collection = get_collection("auth_profiles")
        self.assignments_collection = get_collection("auth_profile_assignments")
        self.usage_logs_collection = get_collection("auth_profile_usage_logs")

        # Cache for frequently accessed profiles
        self._profile_cache: Dict[str, AuthenticationProfile] = {}
        self._cache_expiry: Dict[str, datetime] = {}
        self._cache_ttl = timedelta(minutes=10)
        self._cache_lock = asyncio.Lock()

        # Initialize templates
        self._templates = self._initialize_templates()

    def _initialize_templates(self) -> Dict[str, AuthProfileTemplate]:
        """Initialize built-in authentication profile templates"""
        templates = {}

        # E-commerce/Shopping Cart Template
        templates["ecommerce_standard"] = AuthProfileTemplate(
            template_id="ecommerce_standard",
            template_name="E-commerce Standard Authentication",
            description="Standard authentication for e-commerce and shopping cart APIs",
            provider_name="Generic E-commerce",
            provider_category="ecommerce",
            auth_type=AuthenticationType.BEARER_TOKEN,
            required_fields=["login_token"],
            optional_fields=["session_cookies", "custom_headers"],
            default_headers={
                "Content-Type": "application/json",
                "Accept": "application/json",
                "User-Agent": "Bot/1.0",
            },
            setup_instructions=(
                "1. Log into your e-commerce admin panel\n"
                "2. Navigate to API settings or developer section\n"
                "3. Generate or copy your API token\n"
                "4. Configure any required session cookies\n"
                "5. Test the connection"
            ),
            credential_sources={
                "login_token": "Admin Panel > API Settings > Access Token",
                "session_cookies": "Browser Developer Tools > Application > Cookies",
            },
        )

        # Payment Gateway Template
        templates["payment_gateway"] = AuthProfileTemplate(
            template_id="payment_gateway",
            template_name="Payment Gateway Authentication",
            description="Authentication for payment processing APIs",
            provider_name="Generic Payment Gateway",
            provider_category="payment",
            auth_type=AuthenticationType.API_KEY,
            required_fields=["api_key"],
            optional_fields=["api_secret", "merchant_id"],
            default_headers={
                "Content-Type": "application/json",
                "Accept": "application/json",
            },
            setup_instructions=(
                "1. Access your payment gateway dashboard\n"
                "2. Go to API Keys or Developer section\n"
                "3. Generate new API key and secret\n"
                "4. Note your merchant ID if required\n"
                "5. Configure webhook endpoints if needed"
            ),
            credential_sources={
                "api_key": "Dashboard > API Keys > Public Key",
                "api_secret": "Dashboard > API Keys > Secret Key",
                "merchant_id": "Account Settings > Merchant Information",
            },
        )

        # OAuth2 Template
        templates["oauth2_standard"] = AuthProfileTemplate(
            template_id="oauth2_standard",
            template_name="OAuth2 Standard Authentication",
            description="Standard OAuth2 authentication flow",
            provider_name="Generic OAuth2 Provider",
            provider_category="authentication",
            auth_type=AuthenticationType.OAUTH2,
            required_fields=[
                "oauth2_client_id",
                "oauth2_client_secret",
                "oauth2_token_url",
            ],
            optional_fields=["oauth2_scope", "oauth2_refresh_token"],
            default_headers={
                "Content-Type": "application/x-www-form-urlencoded",
                "Accept": "application/json",
            },
            setup_instructions=(
                "1. Register your application with the OAuth2 provider\n"
                "2. Copy the client ID and client secret\n"
                "3. Configure redirect URIs if required\n"
                "4. Set appropriate scopes for your use case\n"
                "5. Test the authorization flow"
            ),
            credential_sources={
                "oauth2_client_id": "Developer Console > App Settings > Client ID",
                "oauth2_client_secret": "Developer Console > App Settings > Client Secret",
                "oauth2_token_url": "API Documentation > OAuth2 > Token Endpoint",
            },
        )

        return templates

    async def create_profile(
        self,
        profile_name: str,
        display_name: str,
        auth_type: AuthenticationType,
        credentials: Dict[str, Any],
        created_by: str,
        description: Optional[str] = None,
        scope: AuthProfileScope = AuthProfileScope.GLOBAL,
        environment: APIEnvironment = APIEnvironment.DEVELOPMENT,
        **kwargs,
    ) -> Optional[AuthenticationProfile]:
        """Create a new authentication profile"""
        try:
            # Check if profile name already exists
            existing = await self.get_profile_by_name(profile_name)
            if existing:
                logger.warning(f"Profile with name '{profile_name}' already exists")
                return None

            # Encrypt sensitive credentials
            encrypted_credentials = await self._encrypt_credentials(
                credentials, auth_type
            )

            # Create profile
            profile = AuthenticationProfile(
                profile_name=profile_name,
                display_name=display_name,
                description=description,
                auth_type=auth_type,
                credentials=encrypted_credentials,
                scope=scope,
                environment=environment,
                created_by=created_by,
                encryption_key_id=self._generate_key_id(),
                **kwargs,
            )

            # Save to database
            result = await self.profiles_collection.insert_one(profile.dict())
            profile.id = str(result.inserted_id)

            # Cache the profile
            await self._cache_profile(profile)

            logger.info(f"Created authentication profile: {profile_name}")
            return profile

        except Exception as e:
            logger.error(f"Failed to create authentication profile {profile_name}: {e}")
            return None

    async def get_profile_by_name(
        self, profile_name: str
    ) -> Optional[AuthenticationProfile]:
        """Get authentication profile by name"""
        try:
            # Check cache first
            async with self._cache_lock:
                if profile_name in self._profile_cache:
                    cache_time = self._cache_expiry.get(profile_name)
                    if cache_time and datetime.now(timezone.utc) < cache_time:
                        return self._profile_cache[profile_name]

            # Query database
            doc = await self.profiles_collection.find_one(
                {"profile_name": profile_name}
            )
            if not doc:
                return None

            profile = AuthenticationProfile(**doc)

            # Cache the result
            await self._cache_profile(profile)

            return profile

        except Exception as e:
            logger.error(f"Failed to get profile {profile_name}: {e}")
            return None

    async def get_profile_by_id(
        self, profile_id: str
    ) -> Optional[AuthenticationProfile]:
        """Get authentication profile by ID"""
        try:
            from bson import ObjectId

            doc = await self.profiles_collection.find_one({"_id": ObjectId(profile_id)})
            if not doc:
                return None

            profile = AuthenticationProfile(**doc)

            # Cache the result
            await self._cache_profile(profile)

            return profile

        except Exception as e:
            logger.error(f"Failed to get profile by ID {profile_id}: {e}")
            return None

    async def list_profiles(
        self,
        scope: Optional[AuthProfileScope] = None,
        environment: Optional[APIEnvironment] = None,
        status: Optional[AuthProfileStatus] = None,
        category: Optional[str] = None,
    ) -> List[AuthenticationProfile]:
        """List authentication profiles with optional filters"""
        try:
            # Build query
            query = {}
            if scope:
                query["scope"] = scope.value
            if environment:
                query["environment"] = environment.value
            if status:
                query["status"] = status.value
            if category:
                query["allowed_categories"] = {"$in": [category]}

            # Add soft delete filter
            query["is_deleted"] = {"$ne": True}

            # Query database
            cursor = self.profiles_collection.find(query).sort("display_name", 1)
            profiles = []

            async for doc in cursor:
                try:
                    profile = AuthenticationProfile(**doc)
                    profiles.append(profile)
                except Exception as e:
                    logger.warning(f"Failed to parse profile document: {e}")
                    continue

            return profiles

        except Exception as e:
            logger.error(f"Failed to list profiles: {e}")
            return []

    async def update_profile(
        self,
        profile_id: str,
        updates: Dict[str, Any],
        updated_by: str,
        propagate_changes: bool = True,
    ) -> bool:
        """Update authentication profile with optional change propagation"""
        try:
            from bson import ObjectId

            # Get current profile for comparison
            current_profile = await self.get_profile_by_id(profile_id)
            if not current_profile:
                logger.error(f"Profile {profile_id} not found")
                return False

            # Prepare update data
            update_data = updates.copy()
            update_data["last_modified_by"] = updated_by
            update_data["updated_at"] = datetime.now(timezone.utc)

            # Handle credential updates
            if "credentials" in update_data:
                encrypted_credentials = await self._encrypt_credentials(
                    update_data["credentials"], current_profile.auth_type
                )
                update_data["credentials"] = encrypted_credentials

            # Update database
            result = await self.profiles_collection.update_one(
                {"_id": ObjectId(profile_id)}, {"$set": update_data}
            )

            if result.modified_count > 0:
                # Clear cache
                await self._invalidate_cache(profile_id)

                # Propagate changes to assigned API configurations if requested
                if propagate_changes:
                    await self._propagate_profile_changes(
                        profile_id, updates, updated_by, current_profile
                    )

                logger.info(f"Updated authentication profile: {profile_id}")
                return True

            return False

        except Exception as e:
            logger.error(f"Failed to update profile {profile_id}: {e}")
            return False

    async def delete_profile(self, profile_id: str, deleted_by: str) -> bool:
        """Soft delete authentication profile"""
        try:
            from bson import ObjectId

            # Check if profile is in use
            assignments = await self.get_profile_assignments(profile_id)
            if assignments:
                logger.warning(
                    f"Cannot delete profile {profile_id}: still in use by {len(assignments)} APIs"
                )
                return False

            # Soft delete
            result = await self.profiles_collection.update_one(
                {"_id": ObjectId(profile_id)},
                {
                    "$set": {
                        "is_deleted": True,
                        "deleted_at": datetime.now(timezone.utc),
                        "deleted_by": deleted_by,
                    }
                },
            )

            if result.modified_count > 0:
                # Clear cache
                await self._invalidate_cache(profile_id)
                logger.info(f"Deleted authentication profile: {profile_id}")
                return True

            return False

        except Exception as e:
            logger.error(f"Failed to delete profile {profile_id}: {e}")
            return False

    async def get_decrypted_credentials(
        self, profile_id: str
    ) -> Optional[Dict[str, Any]]:
        """Get decrypted credentials for a profile"""
        try:
            profile = await self.get_profile_by_id(profile_id)
            if not profile or not profile.is_active():
                return None

            # Decrypt credentials
            decrypted = await self._decrypt_credentials(
                profile.credentials, profile.auth_type
            )

            # Log usage
            await self._log_usage(profile_id, "decrypt_credentials", True)

            return decrypted

        except Exception as e:
            logger.error(f"Failed to decrypt credentials for profile {profile_id}: {e}")
            await self._log_usage(profile_id, "decrypt_credentials", False, str(e))
            return None

    async def get_templates(self) -> List[AuthProfileTemplate]:
        """Get available authentication profile templates"""
        return list(self._templates.values())

    async def get_template(self, template_id: str) -> Optional[AuthProfileTemplate]:
        """Get specific authentication profile template"""
        return self._templates.get(template_id)

    async def create_profile_from_template(
        self,
        template_id: str,
        profile_name: str,
        credentials: Dict[str, Any],
        created_by: str,
        **kwargs,
    ) -> Optional[AuthenticationProfile]:
        """Create authentication profile from template"""
        template = await self.get_template(template_id)
        if not template:
            logger.error(f"Template {template_id} not found")
            return None

        return await self.create_profile(
            profile_name=profile_name,
            display_name=kwargs.get(
                "display_name", f"{template.provider_name} - {profile_name}"
            ),
            auth_type=template.auth_type,
            credentials=credentials,
            created_by=created_by,
            description=kwargs.get("description", template.description),
            scope=kwargs.get("scope", template.default_scope),
            provider_name=template.provider_name,
            provider_documentation=template.provider_documentation,
            tags=kwargs.get("tags", [template.provider_category]),
            **kwargs,
        )

    # Profile assignment methods

    async def assign_profile_to_api(
        self,
        api_config_id: str,
        auth_profile_id: str,
        assigned_by: str,
        override_credentials: Optional[Dict[str, Any]] = None,
        override_headers: Optional[Dict[str, str]] = None,
    ) -> bool:
        """Assign authentication profile to API configuration"""
        try:
            # Check if profile exists and is active
            profile = await self.get_profile_by_id(auth_profile_id)
            if not profile or not profile.is_active():
                logger.error(f"Profile {auth_profile_id} not found or inactive")
                return False

            # Check for existing assignment
            existing = await self.assignments_collection.find_one(
                {"api_config_id": api_config_id, "is_active": True}
            )

            if existing:
                # Update existing assignment
                result = await self.assignments_collection.update_one(
                    {"_id": existing["_id"]},
                    {
                        "$set": {
                            "auth_profile_id": auth_profile_id,
                            "override_credentials": override_credentials or {},
                            "override_headers": override_headers or {},
                            "assigned_by": assigned_by,
                            "updated_at": datetime.now(timezone.utc),
                        }
                    },
                )
            else:
                # Create new assignment
                assignment = AuthProfileAssignment(
                    api_config_id=api_config_id,
                    auth_profile_id=auth_profile_id,
                    assigned_by=assigned_by,
                    override_credentials=override_credentials or {},
                    override_headers=override_headers or {},
                )
                result = await self.assignments_collection.insert_one(assignment.dict())

            # Update profile usage count
            await self.profiles_collection.update_one(
                {"_id": profile.id},
                {
                    "$inc": {"usage_count": 1},
                    "$set": {"last_used_at": datetime.now(timezone.utc)},
                },
            )

            logger.info(f"Assigned profile {auth_profile_id} to API {api_config_id}")
            return True

        except Exception as e:
            logger.error(
                f"Failed to assign profile {auth_profile_id} to API {api_config_id}: {e}"
            )
            return False

    async def unassign_profile_from_api(self, api_config_id: str) -> bool:
        """Remove profile assignment from API configuration"""
        try:
            result = await self.assignments_collection.update_one(
                {"api_config_id": api_config_id, "is_active": True},
                {
                    "$set": {
                        "is_active": False,
                        "updated_at": datetime.now(timezone.utc),
                    }
                },
            )

            if result.modified_count > 0:
                logger.info(f"Unassigned profile from API {api_config_id}")
                return True

            return False

        except Exception as e:
            logger.error(f"Failed to unassign profile from API {api_config_id}: {e}")
            return False

    async def get_api_profile_assignment(
        self, api_config_id: str
    ) -> Optional[AuthProfileAssignment]:
        """Get active profile assignment for API configuration"""
        try:
            doc = await self.assignments_collection.find_one(
                {"api_config_id": api_config_id, "is_active": True}
            )

            if doc:
                return AuthProfileAssignment(**doc)

            return None

        except Exception as e:
            logger.error(
                f"Failed to get profile assignment for API {api_config_id}: {e}"
            )
            return None

    async def get_profile_assignments(
        self, auth_profile_id: str
    ) -> List[AuthProfileAssignment]:
        """Get all active assignments for a profile"""
        try:
            cursor = self.assignments_collection.find(
                {"auth_profile_id": auth_profile_id, "is_active": True}
            )

            assignments = []
            async for doc in cursor:
                try:
                    assignment = AuthProfileAssignment(**doc)
                    assignments.append(assignment)
                except Exception as e:
                    logger.warning(f"Failed to parse assignment document: {e}")
                    continue

            return assignments

        except Exception as e:
            logger.error(
                f"Failed to get assignments for profile {auth_profile_id}: {e}"
            )
            return []

    # Utility methods

    async def _encrypt_credentials(
        self, credentials: Dict[str, Any], auth_type: AuthenticationType
    ) -> Dict[str, Any]:
        """Encrypt sensitive credential fields"""
        encrypted = credentials.copy()

        # Define sensitive fields by auth type
        sensitive_fields = {
            AuthenticationType.API_KEY: ["api_key"],
            AuthenticationType.BEARER_TOKEN: ["bearer_token", "login_token"],
            AuthenticationType.BASIC_AUTH: ["password"],
            AuthenticationType.OAUTH2: ["oauth2_client_secret", "oauth2_refresh_token"],
            AuthenticationType.CUSTOM_HEADER: [],  # Custom headers may contain sensitive data
        }

        # Always encrypt these fields regardless of auth type
        always_encrypt = ["password", "secret", "token", "key"]

        fields_to_encrypt = sensitive_fields.get(auth_type, [])

        # Add fields that contain sensitive keywords
        for field, value in credentials.items():
            if any(keyword in field.lower() for keyword in always_encrypt):
                if field not in fields_to_encrypt:
                    fields_to_encrypt.append(field)

        # Encrypt the fields
        for field in fields_to_encrypt:
            if field in encrypted and encrypted[field]:
                try:
                    encrypted[field] = self.encryption.encrypt(str(encrypted[field]))
                except Exception as e:
                    logger.warning(f"Failed to encrypt field {field}: {e}")

        return encrypted

    async def _decrypt_credentials(
        self, encrypted_credentials: Dict[str, Any], auth_type: AuthenticationType
    ) -> Dict[str, Any]:
        """Decrypt sensitive credential fields"""
        decrypted = encrypted_credentials.copy()

        # Use same logic as encryption to determine which fields to decrypt
        sensitive_fields = {
            AuthenticationType.API_KEY: ["api_key"],
            AuthenticationType.BEARER_TOKEN: ["bearer_token", "login_token"],
            AuthenticationType.BASIC_AUTH: ["password"],
            AuthenticationType.OAUTH2: ["oauth2_client_secret", "oauth2_refresh_token"],
            AuthenticationType.CUSTOM_HEADER: [],
        }

        always_decrypt = ["password", "secret", "token", "key"]
        fields_to_decrypt = sensitive_fields.get(auth_type, [])

        for field, value in encrypted_credentials.items():
            if any(keyword in field.lower() for keyword in always_decrypt):
                if field not in fields_to_decrypt:
                    fields_to_decrypt.append(field)

        # Decrypt the fields
        for field in fields_to_decrypt:
            if field in decrypted and decrypted[field]:
                try:
                    decrypted[field] = self.encryption.decrypt(str(decrypted[field]))
                except Exception as e:
                    logger.warning(f"Failed to decrypt field {field}: {e}")
                    decrypted[field] = "[DECRYPTION_FAILED]"

        return decrypted

    async def _cache_profile(self, profile: AuthenticationProfile):
        """Cache authentication profile"""
        async with self._cache_lock:
            self._profile_cache[profile.profile_name] = profile
            self._cache_expiry[profile.profile_name] = (
                datetime.now(timezone.utc) + self._cache_ttl
            )

    async def _invalidate_cache(self, profile_id: str):
        """Invalidate cached profile"""
        async with self._cache_lock:
            # Find and remove from cache by ID
            to_remove = []
            for name, profile in self._profile_cache.items():
                if str(profile.id) == profile_id:
                    to_remove.append(name)

            for name in to_remove:
                self._profile_cache.pop(name, None)
                self._cache_expiry.pop(name, None)

    async def _log_usage(
        self,
        auth_profile_id: str,
        operation: str,
        success: bool,
        error_message: Optional[str] = None,
        **kwargs,
    ):
        """Log authentication profile usage"""
        try:
            log_entry = AuthProfileUsageLog(
                auth_profile_id=auth_profile_id,
                api_config_id=kwargs.get("api_config_id", ""),
                operation=operation,
                success=success,
                error_message=error_message,
                user_id=kwargs.get("user_id"),
                endpoint_used=kwargs.get("endpoint_used"),
                response_time_ms=kwargs.get("response_time_ms"),
                environment=kwargs.get("environment", APIEnvironment.DEVELOPMENT),
                additional_data=kwargs.get("additional_data", {}),
            )

            await self.usage_logs_collection.insert_one(log_entry.dict())

        except Exception as e:
            logger.warning(f"Failed to log usage for profile {auth_profile_id}: {e}")

    def _generate_key_id(self) -> str:
        """Generate encryption key ID"""
        import hashlib
        import time

        # Use current time and settings to generate a unique key ID
        key_data = f"{time.time()}_{self.settings.API_CONFIG_ENCRYPTION_KEY[:10]}"
        return hashlib.md5(key_data.encode()).hexdigest()[:16]

    # Bulk update and propagation methods

    async def _propagate_profile_changes(
        self,
        profile_id: str,
        updates: Dict[str, Any],
        updated_by: str,
        original_profile: AuthenticationProfile,
    ) -> Dict[str, Any]:
        """Propagate authentication profile changes to all assigned API configurations"""
        try:
            # Get all assignments for this profile
            assignments = await self.get_profile_assignments(profile_id)

            if not assignments:
                logger.info(f"No API assignments found for profile {profile_id}")
                return {"updated_apis": 0, "errors": []}

            # Import API config service (avoid circular import)
            from services.api_config_service import get_api_config_service

            api_service = get_api_config_service()

            updated_apis = 0
            errors = []

            # Process each assignment
            for assignment in assignments:
                try:
                    api_config_id = assignment.api_config_id

                    # Get current API configuration
                    config = await api_service.get_api_config(api_config_id)
                    if not config:
                        errors.append(f"API configuration {api_config_id} not found")
                        continue

                    # Check if this API is still using the profile
                    if (
                        not config.credentials.use_profile
                        or config.credentials.auth_profile_id != profile_id
                    ):
                        logger.warning(
                            f"API {api_config_id} no longer uses profile {profile_id}"
                        )
                        continue

                    # Determine what needs to be updated based on the profile changes
                    needs_update = False

                    # Check if credentials changed
                    if "credentials" in updates:
                        needs_update = True
                        logger.info(
                            f"Profile credentials changed, updating API {api_config_id}"
                        )

                    # Check if headers changed (if profile has default headers)
                    if "default_headers" in updates:
                        needs_update = True
                        logger.info(
                            f"Profile headers changed, updating API {api_config_id}"
                        )

                    # Check if profile status changed
                    if "status" in updates:
                        new_status = updates["status"]
                        if new_status != original_profile.status:
                            needs_update = True
                            logger.info(
                                f"Profile status changed to {new_status}, updating API {api_config_id}"
                            )

                    if needs_update:
                        # Update the API configuration's last_updated timestamp
                        # The actual credential merging will happen when get_effective_credentials is called
                        config.last_updated = datetime.now(timezone.utc)

                        # Save the updated configuration
                        success = await api_service.save_api_config(
                            config, config.source, updated_by
                        )

                        if success:
                            updated_apis += 1

                            # Log the propagation
                            await self._log_usage(
                                profile_id,
                                "profile_change_propagated",
                                True,
                                api_config_id=api_config_id,
                                user_id=updated_by,
                                additional_data={
                                    "changes": list(updates.keys()),
                                    "assignment_id": (
                                        str(assignment.id)
                                        if hasattr(assignment, "id")
                                        else None
                                    ),
                                },
                            )
                        else:
                            errors.append(
                                f"Failed to update API configuration {api_config_id}"
                            )

                except Exception as e:
                    error_msg = (
                        f"Failed to update API {assignment.api_config_id}: {str(e)}"
                    )
                    errors.append(error_msg)
                    logger.error(error_msg)
                    continue

            result = {
                "updated_apis": updated_apis,
                "total_assignments": len(assignments),
                "errors": errors,
            }

            logger.info(
                f"Profile {profile_id} changes propagated to {updated_apis}/{len(assignments)} APIs"
            )

            return result

        except Exception as e:
            logger.error(f"Failed to propagate profile changes for {profile_id}: {e}")
            return {"updated_apis": 0, "errors": [str(e)]}

    async def bulk_update_profiles(
        self,
        profile_updates: Dict[str, Dict[str, Any]],
        updated_by: str,
        propagate_changes: bool = True,
    ) -> Dict[str, Any]:
        """Bulk update multiple authentication profiles"""
        try:
            results = {
                "successful_updates": 0,
                "failed_updates": 0,
                "total_api_updates": 0,
                "errors": [],
                "profile_results": {},
            }

            for profile_id, updates in profile_updates.items():
                try:
                    success = await self.update_profile(
                        profile_id, updates, updated_by, propagate_changes
                    )

                    if success:
                        results["successful_updates"] += 1
                        results["profile_results"][profile_id] = {"success": True}

                        # If propagation was enabled, get the propagation results
                        if propagate_changes:
                            # The propagation results are logged internally
                            # We could enhance this to return detailed results if needed
                            pass
                    else:
                        results["failed_updates"] += 1
                        results["profile_results"][profile_id] = {
                            "success": False,
                            "error": "Update failed",
                        }
                        results["errors"].append(
                            f"Failed to update profile {profile_id}"
                        )

                except Exception as e:
                    results["failed_updates"] += 1
                    error_msg = f"Error updating profile {profile_id}: {str(e)}"
                    results["errors"].append(error_msg)
                    results["profile_results"][profile_id] = {
                        "success": False,
                        "error": str(e),
                    }

            logger.info(
                f"Bulk update completed: {results['successful_updates']} successful, {results['failed_updates']} failed"
            )

            return results

        except Exception as e:
            logger.error(f"Bulk profile update failed: {e}")
            return {
                "successful_updates": 0,
                "failed_updates": len(profile_updates),
                "total_api_updates": 0,
                "errors": [str(e)],
                "profile_results": {},
            }

    async def get_profile_impact_analysis(self, profile_id: str) -> Dict[str, Any]:
        """Analyze the impact of changing or deleting a profile"""
        try:
            profile = await self.get_profile_by_id(profile_id)
            if not profile:
                return {"error": "Profile not found"}

            assignments = await self.get_profile_assignments(profile_id)

            # Import API config service
            from services.api_config_service import get_api_config_service

            api_service = get_api_config_service()

            impact_analysis = {
                "profile_name": profile.profile_name,
                "profile_display_name": profile.display_name,
                "total_assignments": len(assignments),
                "active_apis": [],
                "inactive_apis": [],
                "apis_with_overrides": [],
                "apis_without_overrides": [],
                "categories_affected": set(),
                "environments_affected": set(),
            }

            for assignment in assignments:
                try:
                    config = await api_service.get_api_config(assignment.api_config_id)
                    if not config:
                        continue

                    api_info = {
                        "service_name": config.service_name,
                        "display_name": config.display_name,
                        "category": config.category,
                        "environment": config.environment,
                        "enabled": config.enabled,
                        "has_overrides": bool(
                            assignment.override_credentials
                            or assignment.override_headers
                        ),
                    }

                    if config.enabled:
                        impact_analysis["active_apis"].append(api_info)
                    else:
                        impact_analysis["inactive_apis"].append(api_info)

                    if api_info["has_overrides"]:
                        impact_analysis["apis_with_overrides"].append(api_info)
                    else:
                        impact_analysis["apis_without_overrides"].append(api_info)

                    impact_analysis["categories_affected"].add(config.category)
                    impact_analysis["environments_affected"].add(config.environment)

                except Exception as e:
                    logger.warning(
                        f"Failed to analyze API {assignment.api_config_id}: {e}"
                    )
                    continue

            # Convert sets to lists for JSON serialization
            impact_analysis["categories_affected"] = list(
                impact_analysis["categories_affected"]
            )
            impact_analysis["environments_affected"] = list(
                impact_analysis["environments_affected"]
            )

            return impact_analysis

        except Exception as e:
            logger.error(f"Failed to analyze profile impact for {profile_id}: {e}")
            return {"error": str(e)}


# Global instance
_auth_profile_service = None


def get_auth_profile_service() -> AuthProfileService:
    """Get the global authentication profile service instance"""
    global _auth_profile_service
    if _auth_profile_service is None:
        _auth_profile_service = AuthProfileService()
    return _auth_profile_service
