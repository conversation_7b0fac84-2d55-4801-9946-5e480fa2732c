"""
Comprehensive External API Integration Service

This service provides a consolidated interface for all external API operations
including list, add_to_cart, view_cart, delete_from_cart, and user authentication.
It integrates with the existing admin panel for configuration management.

Based on the demo API examples provided in the demo folder.
"""

from __future__ import annotations

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime, timezone
import json
from dataclasses import dataclass, field
from enum import Enum
import time
import aiohttp
from aiohttp import ClientTimeout, ClientSession

from services.api_config_service import get_api_config_service, APIConfiguration
from config.settings import get_settings
from utils.logging import get_logger
from utils.performance import monitor_performance
from utils.api_logging import get_api_logger, LogLevel

logger = get_logger(__name__)
api_logger = get_api_logger("external_api_service", LogLevel.DEBUG)


class APIOperation(str, Enum):
    """Supported API operations"""

    LIST_ITEMS = "list_items"
    ADD_TO_CART = "add_to_cart"
    VIEW_CART = "view_cart"
    DELETE_FROM_CART = "delete_from_cart"
    GET_USER_INFO = "get_user_info"


@dataclass
class APIResponse:
    """Standardized API response wrapper"""

    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    status_code: Optional[int] = None
    raw_response: Optional[str] = None
    operation: Optional[APIOperation] = None
    execution_time: Optional[float] = None


@dataclass
class ListItemsParams:
    """Parameters for list items operation"""

    page: int = 1
    limit: int = 10
    base: str = ""
    bank: str = ""
    bin: str = ""
    country: str = ""
    state: str = ""
    city: str = ""
    brand: str = ""
    type: str = ""
    zip: str = ""
    price_from: float = 0
    price_to: float = 500
    zip_check: bool = False
    address: bool = False
    phone: bool = False
    email: bool = False
    without_cvv: bool = False
    refundable: bool = False
    expire_this_month: bool = False
    dob: bool = False
    ssn: bool = False
    mmn: bool = False
    ip: bool = False
    dl: bool = False
    ua: bool = False
    discount: bool = False


@dataclass
class ExternalAPIConfig:
    """Configuration for external API service"""

    base_url: str
    login_token: str
    session_cookies: Dict[str, str] = field(default_factory=dict)
    headers: Dict[str, str] = field(default_factory=dict)
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0


class ExternalAPIService:
    """
    Comprehensive external API service for all operations

    This service handles:
    - List items with filtering
    - Add items to cart
    - View cart contents
    - Delete items from cart
    - User authentication and session management
    """

    def __init__(self):
        self.settings = get_settings()
        self.api_config_service = get_api_config_service()
        self._session: Optional[ClientSession] = None
        self._config_cache: Optional[ExternalAPIConfig] = None
        self._cache_expiry: Optional[datetime] = None
        self._cache_ttl_minutes = 30

        # Dynamic cookie storage for server-provided cookies
        self._dynamic_cookies: Dict[str, str] = {}

    async def __aenter__(self):
        """Async context manager entry"""
        await self._ensure_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()

    async def close(self):
        """Close the HTTP session"""
        if self._session and not self._session.closed:
            await self._session.close()
            self._session = None

    async def _ensure_session(self) -> ClientSession:
        """Ensure HTTP session is available"""
        if self._session is None or self._session.closed:
            timeout = ClientTimeout(total=30, connect=10)
            self._session = ClientSession(timeout=timeout)
        return self._session

    async def _get_api_config(self) -> Optional[ExternalAPIConfig]:
        """Get API configuration with caching"""
        try:
            # Check cache validity
            if (
                self._config_cache
                and self._cache_expiry
                and datetime.now(timezone.utc) < self._cache_expiry
            ):
                return self._config_cache

            # Load configuration from API config service (new name)
            api_config = await self.api_config_service.get_api_config(
                "api1_external_cart"
            )

            # Backward compatibility: try legacy name if new name not configured
            if not api_config:
                try:
                    legacy = await self.api_config_service.get_api_config(
                        "external_cart"
                    )
                except Exception:
                    legacy = None
                if legacy:
                    logger.warning(
                        "Using legacy API configuration 'external_cart'; consider renaming to 'api1_external_cart'"
                    )
                    api_config = legacy

            if not api_config:
                logger.warning(
                    "No api1_external_cart API configuration found, using default"
                )
                return self._get_default_config()

            # Check if configuration has valid credentials
            has_valid_credentials = (
                api_config.credentials
                and api_config.credentials.login_token
                and api_config.credentials.session_cookies
            )

            if not has_valid_credentials:
                logger.warning("API configuration has empty credentials, using default")
                return self._get_default_config()

            # Extract configuration details
            config = ExternalAPIConfig(
                base_url=api_config.base_url,
                login_token=api_config.credentials.login_token,
                session_cookies=api_config.credentials.session_cookies,
                headers=(
                    api_config.credentials.headers
                    if api_config.credentials
                    else self._get_default_headers()
                ),
                timeout=30,
                max_retries=3,
                retry_delay=1.0,
            )

            # Update cache
            self._config_cache = config
            from datetime import timedelta

            self._cache_expiry = datetime.now(timezone.utc) + timedelta(
                minutes=self._cache_ttl_minutes
            )

            return config

        except Exception as e:
            logger.error(f"Error loading API configuration: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> ExternalAPIConfig:
        """Get default configuration based on working demo patterns"""
        # Use the working JWT token from demo files
        jwt_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTk5MDk0LCJpYXQiOjE3NTczNTQ3NDYsImV4cCI6MTc1OTk0Njc0Nn0.iXL_3zO-VJ2elJ4furlJ_FVSZONnRwogtQdZofX3M6o"

        return ExternalAPIConfig(
            base_url="https://ronaldo-club.to/api",
            login_token=jwt_token,
            session_cookies={
                "__ddg1_": "u1UaBqLkngSC4ZTJRDQC",
                "testcookie": "1",
                # Use cart-specific cookies from demo/add_to_cart.py for better compatibility
                "__ddg8_": "VIywnRkIRMee1yvS",  # Cart-specific values from demo
                "__ddg9_": "118.99.2.9",  # Cart-specific values from demo
                "__ddg10_": "1757322900",  # Cart-specific values from demo
            },
            headers=self._get_default_headers(),
            timeout=30,
            max_retries=3,
            retry_delay=1.0,
        )

    def _get_default_headers(self) -> Dict[str, str]:
        """Get default headers based on demo examples"""
        return {
            "accept": "application/json, text/plain, */*",
            "accept-language": "en-US,en;q=0.9",
            "content-length": "0",
            "origin": "https://ronaldo-club.to",
            "priority": "u=1, i",
            "referer": "https://ronaldo-club.to/store/cards/hq",
            "sec-ch-ua": '"Chromium";v="140", "Not=A?Brand";v="24", "Brave";v="140"',
            "sec-ch-ua-mobile": "?1",
            "sec-ch-ua-platform": '"Android"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "sec-gpc": "1",
            "user-agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/140.0.0.0 Mobile Safari/537.36",
        }

    def _build_headers(
        self, config: ExternalAPIConfig, operation: APIOperation
    ) -> Dict[str, str]:
        """Build headers for specific operation"""
        # Base headers from configuration
        headers = config.headers.copy()

        # Include Authorization bearer token if available (some endpoints rely on it)
        if config.login_token and "Authorization" not in headers:
            headers["Authorization"] = f"Bearer {config.login_token}"

        # Operation-specific header adjustments
        if operation == APIOperation.LIST_ITEMS:
            headers.update(
                {
                    "content-length": "0",  # POST with no body
                    "referer": "https://ronaldo-club.to/store/cards/hq",
                }
            )
        elif operation == APIOperation.ADD_TO_CART:
            headers.update(
                {
                    "content-type": "application/json",
                    "referer": "https://ronaldo-club.to/store/cards/hq",
                }
            )
            # Let aiohttp compute actual content-length for JSON payloads
            headers.pop("content-length", None)
        elif operation == APIOperation.VIEW_CART:
            headers.update({"referer": "https://ronaldo-club.to/store/cart"})
            # Remove content-type for GET request
            headers.pop("content-type", None)
            headers.pop("content-length", None)
        elif operation == APIOperation.DELETE_FROM_CART:
            headers.update({"referer": "https://ronaldo-club.to/store/cart"})
            # Remove content-type for DELETE request
            headers.pop("content-type", None)
            headers.pop("content-length", None)
        elif operation == APIOperation.GET_USER_INFO:
            headers.update(
                {
                    "content-type": "application/json",
                    "referer": "https://ronaldo-club.to/",
                }
            )
            # Let aiohttp compute content-length if any body is used
            headers.pop("content-length", None)

        return headers

    def _build_cookies(self, config: ExternalAPIConfig) -> Dict[str, str]:
        """Build cookies with login token and dynamic server-provided cookies"""
        cookies = config.session_cookies.copy()

        # Update with any dynamic cookies provided by the server
        cookies.update(self._dynamic_cookies)

        # Ensure login token is included
        if config.login_token:
            cookies["loginToken"] = config.login_token

        # Ensure testcookie is set
        cookies["testcookie"] = "1"

        return cookies

    def _update_dynamic_cookies(self, response_headers: Dict[str, str]) -> None:
        """Update dynamic cookies from server response"""
        try:
            set_cookie_header = response_headers.get("Set-Cookie", "")
            if not set_cookie_header:
                return

            # Parse Set-Cookie header for __ddg* cookies
            import re

            # Look for __ddg8_, __ddg9_, __ddg10_ cookies
            ddg_patterns = [r"__ddg8_=([^;]+)", r"__ddg9_=([^;]+)", r"__ddg10_=([^;]+)"]

            for pattern in ddg_patterns:
                match = re.search(pattern, set_cookie_header)
                if match:
                    # Extract cookie name from pattern
                    if "__ddg8_" in pattern:
                        cookie_name = "__ddg8_"
                    elif "__ddg9_" in pattern:
                        cookie_name = "__ddg9_"
                    elif "__ddg10_" in pattern:
                        cookie_name = "__ddg10_"
                    else:
                        continue

                    cookie_value = match.group(1)
                    self._dynamic_cookies[cookie_name] = cookie_value
                    logger.debug(
                        f"Updated dynamic cookie {cookie_name}: {cookie_value}"
                    )

        except Exception as e:
            logger.debug(f"Error updating dynamic cookies: {e}")

    def _build_list_url(
        self, config: ExternalAPIConfig, params: ListItemsParams
    ) -> str:
        """Build URL for list items operation"""
        base_url = f"{config.base_url}/cards/hq/list"

        # Build query parameters
        query_params = [
            f"page={params.page}",
            f"limit={params.limit}",
            f"base={params.base}",
            f"bank={params.bank}",
            f"bin={params.bin}",
            f"country={params.country}",
            f"state={params.state}",
            f"city={params.city}",
            f"brand={params.brand}",
            f"type={params.type}",
            f"zip={params.zip}",
            f"priceFrom={params.price_from}",
            f"priceTo={params.price_to}",
            f"zipCheck={'true' if params.zip_check else 'false'}",
            f"address={'true' if params.address else 'false'}",
            f"phone={'true' if params.phone else 'false'}",
            f"email={'true' if params.email else 'false'}",
            f"withoutcvv={'true' if params.without_cvv else 'false'}",
            f"refundable={'true' if params.refundable else 'false'}",
            f"expirethismonth={'true' if params.expire_this_month else 'false'}",
            f"dob={'true' if params.dob else 'false'}",
            f"ssn={'true' if params.ssn else 'false'}",
            f"mmn={'true' if params.mmn else 'false'}",
            f"ip={'true' if params.ip else 'false'}",
            f"dl={'true' if params.dl else 'false'}",
            f"ua={'true' if params.ua else 'false'}",
            f"discount={'true' if params.discount else 'false'}",
        ]

        return f"{base_url}?{'&'.join(query_params)}"

    async def _make_request(
        self,
        method: str,
        url: str,
        headers: Dict[str, str],
        cookies: Dict[str, str],
        operation: APIOperation,
        json_data: Optional[Dict[str, Any]] = None,
        max_retries: int = 0,
        user_id: Optional[str] = None,
    ) -> APIResponse:
        """Make HTTP request with comprehensive logging and retry logic"""
        start_time = time.time()
        session = await self._ensure_session()

        # Create logging context
        context = api_logger.create_context(user_id=user_id, operation=operation.value)

        for attempt in range(max_retries + 1):
            try:
                # Log authentication context
                auth_method = (
                    "bearer_token"
                    if headers.get("Authorization")
                    else "session_cookies" if cookies else "none"
                )
                token_valid = bool(
                    headers.get("Authorization") or cookies.get("loginToken")
                )

                api_logger.log_authentication_context(
                    context=context,
                    auth_method=auth_method,
                    token_valid=token_valid,
                    user_permissions=["api_access"] if token_valid else [],
                    rate_limit_info={},
                )

                # Log the API request
                api_logger.log_request(
                    context=context,
                    method=method,
                    url=url,
                    headers=headers,
                    query_params=None,  # URL already contains query params
                    body=json_data,
                    timeout=30.0,
                    retry_count=attempt,
                )

                logger.debug(
                    f"Making {method} request to {url} (attempt {attempt + 1})"
                )

                async with session.request(
                    method=method,
                    url=url,
                    headers=headers,
                    cookies=cookies,
                    json=json_data,
                    timeout=30,
                ) as response:
                    execution_time = time.time() - start_time
                    raw_response = await response.text()
                    response_headers = dict(response.headers)

                    logger.debug(f"Response status: {response.status}")
                    logger.debug(f"Response body: {raw_response[:500]}...")

                    # Update dynamic cookies from server response
                    self._update_dynamic_cookies(response_headers)

                    # Log the API response
                    api_logger.log_response(
                        context=context,
                        status_code=response.status,
                        status_message=response.reason or "Unknown",
                        headers=response_headers,
                        body=raw_response,
                        error_type="http_error" if response.status >= 400 else None,
                        error_message=(
                            f"HTTP {response.status}"
                            if response.status >= 400
                            else None
                        ),
                    )

                    # Parse JSON response
                    try:
                        data = json.loads(raw_response) if raw_response else {}
                    except json.JSONDecodeError:
                        logger.warning(
                            f"Failed to parse JSON response: {raw_response[:200]}"
                        )

                        # Log JSON parsing error
                        api_logger.log_response(
                            context=context,
                            status_code=response.status,
                            status_message="JSON Parse Error",
                            headers=response_headers,
                            body=raw_response[:1000],
                            error_type="json_parse_error",
                            error_message="Failed to parse JSON response",
                        )

                        data = {"raw_response": raw_response}

                    # Special handling for 403 Forbidden errors
                    if response.status == 403:
                        api_logger.log_403_error_context(
                            context=context,
                            endpoint=url,
                            auth_method=auth_method,
                            token_status="valid" if token_valid else "invalid",
                            user_role="user" if token_valid else "anonymous",
                            required_permissions=["api_access", operation.value],
                            rate_limit_headers={
                                k: v
                                for k, v in response_headers.items()
                                if k.lower().startswith(
                                    ("x-rate", "x-ratelimit", "retry-after")
                                )
                            },
                        )

                    # Determine success based on status code and response content
                    # CRITICAL FIX: Don't default to True if "success" field is missing
                    # The external API should explicitly return {"success": true} for successful operations
                    if response.status == 200 and isinstance(data, dict):
                        # Check if the response explicitly indicates success
                        api_success = data.get("success")
                        if api_success is True:
                            success = True
                        elif api_success is False:
                            success = False
                        else:
                            # If "success" field is missing or not boolean, consider it a failure
                            # This prevents false positives where API returns 200 but operation failed
                            success = False
                            logger.warning(
                                f"API response missing or invalid 'success' field: {data}"
                            )
                    else:
                        success = False

                    return APIResponse(
                        success=success,
                        data=data,
                        status_code=response.status,
                        raw_response=raw_response,
                        operation=operation,
                        execution_time=execution_time,
                        error=(
                            None
                            if success
                            else f"HTTP {response.status}: {raw_response[:200]}"
                        ),
                    )

            except asyncio.TimeoutError:
                error_msg = f"Request timeout on attempt {attempt + 1}"
                logger.warning(error_msg)
                if attempt == max_retries:
                    return APIResponse(
                        success=False,
                        error=f"Request timeout after {max_retries + 1} attempts",
                        operation=operation,
                        execution_time=time.time() - start_time,
                    )
                await asyncio.sleep(1.0 * (attempt + 1))  # Exponential backoff

            except Exception as e:
                error_msg = f"Request error on attempt {attempt + 1}: {str(e)}"
                logger.error(error_msg)
                if attempt == max_retries:
                    return APIResponse(
                        success=False,
                        error=f"Request failed after {max_retries + 1} attempts: {str(e)}",
                        operation=operation,
                        execution_time=time.time() - start_time,
                    )
                await asyncio.sleep(1.0 * (attempt + 1))  # Exponential backoff

    @monitor_performance("list_items")
    async def list_items(
        self, params: Optional[ListItemsParams] = None, user_id: Optional[str] = None
    ) -> APIResponse:
        """
        List items with filtering parameters

        Based on demo/list.py - Uses POST request with query parameters
        """
        try:
            config = await self._get_api_config()
            if not config:
                return APIResponse(
                    success=False,
                    error="API configuration not available",
                    operation=APIOperation.LIST_ITEMS,
                )

            # Use default parameters if none provided
            if params is None:
                params = ListItemsParams()

            # Build request components
            url = self._build_list_url(config, params)
            headers = self._build_headers(config, APIOperation.LIST_ITEMS)
            cookies = self._build_cookies(config)

            logger.info(
                f"Listing items with params: page={params.page}, limit={params.limit}"
            )

            # Make POST request (as per demo example)
            return await self._make_request(
                method="POST",
                url=url,
                headers=headers,
                cookies=cookies,
                operation=APIOperation.LIST_ITEMS,
                user_id=user_id,
            )

        except Exception as e:
            logger.error(f"Error in list_items: {e}")
            return APIResponse(
                success=False,
                error=f"List items operation failed: {str(e)}",
                operation=APIOperation.LIST_ITEMS,
            )

    @monitor_performance("add_to_cart")
    async def add_to_cart(
        self, item_id: int, product_table_name: str = "Cards"
    ) -> APIResponse:
        """
        Add item to cart

        Based on demo/add_to_cart.py - Uses POST request with JSON payload
        """
        try:
            config = await self._get_api_config()
            if not config:
                return APIResponse(
                    success=False,
                    error="API configuration not available",
                    operation=APIOperation.ADD_TO_CART,
                )

            # Build request components
            url = f"{config.base_url}/cart/"
            headers = self._build_headers(config, APIOperation.ADD_TO_CART)
            cookies = self._build_cookies(config)

            # Payload as per demo example
            payload = {"id": item_id, "product_table_name": product_table_name}

            logger.info(f"Adding item {item_id} to cart")
            logger.debug(f"Add to cart request details:")
            logger.debug(f"  URL: {url}")
            logger.debug(f"  Payload: {payload}")
            logger.debug(f"  Headers: {dict(headers)}")
            logger.debug(f"  Cookies: {list(cookies.keys()) if cookies else 'None'}")

            # Make POST request with JSON payload
            response = await self._make_request(
                method="POST",
                url=url,
                headers=headers,
                cookies=cookies,
                json_data=payload,
                operation=APIOperation.ADD_TO_CART,
            )

            # Enhanced logging for add_to_cart response
            logger.info(f"Add to cart response for item {item_id}:")
            logger.info(f"  Success: {response.success}")
            logger.info(f"  Status Code: {response.status_code}")
            logger.info(f"  Response Data: {response.data}")
            logger.info(
                f"  Raw Response: {response.raw_response[:500] if response.raw_response else 'None'}"
            )

            if not response.success:
                logger.error(f"Add to cart failed for item {item_id}: {response.error}")
                if response.data and isinstance(response.data, dict):
                    # Log any additional error details from the API response
                    error_details = (
                        response.data.get("error")
                        or response.data.get("message")
                        or response.data.get("details")
                    )
                    if error_details:
                        logger.error(f"  API Error Details: {error_details}")

            return response

        except Exception as e:
            logger.error(f"Error in add_to_cart: {e}")
            return APIResponse(
                success=False,
                error=f"Add to cart operation failed: {str(e)}",
                operation=APIOperation.ADD_TO_CART,
            )

    @monitor_performance("view_cart")
    async def view_cart(self) -> APIResponse:
        """
        View cart contents

        Based on demo/view_cart.py - Uses GET request
        """
        try:
            config = await self._get_api_config()
            if not config:
                return APIResponse(
                    success=False,
                    error="API configuration not available",
                    operation=APIOperation.VIEW_CART,
                )

            # Build request components
            url = f"{config.base_url}/cart/"
            headers = self._build_headers(config, APIOperation.VIEW_CART)
            cookies = self._build_cookies(config)

            logger.info("Viewing cart contents")

            # Make GET request
            return await self._make_request(
                method="GET",
                url=url,
                headers=headers,
                cookies=cookies,
                operation=APIOperation.VIEW_CART,
            )

        except Exception as e:
            logger.error(f"Error in view_cart: {e}")
            return APIResponse(
                success=False,
                error=f"View cart operation failed: {str(e)}",
                operation=APIOperation.VIEW_CART,
            )

    @monitor_performance("delete_from_cart")
    async def delete_from_cart(self, cart_item_id: int) -> APIResponse:
        """
        Delete item from cart

        Based on demo/delete_from_cart.py - Uses DELETE request with item ID in URL
        """
        try:
            config = await self._get_api_config()
            if not config:
                return APIResponse(
                    success=False,
                    error="API configuration not available",
                    operation=APIOperation.DELETE_FROM_CART,
                )

            # Build request components - item ID is part of URL
            url = f"{config.base_url}/cart/{cart_item_id}"
            headers = self._build_headers(config, APIOperation.DELETE_FROM_CART)
            cookies = self._build_cookies(config)

            logger.info(f"Deleting cart item {cart_item_id}")

            # Make DELETE request
            return await self._make_request(
                method="DELETE",
                url=url,
                headers=headers,
                cookies=cookies,
                operation=APIOperation.DELETE_FROM_CART,
            )

        except Exception as e:
            logger.error(f"Error in delete_from_cart: {e}")
            return APIResponse(
                success=False,
                error=f"Delete from cart operation failed: {str(e)}",
                operation=APIOperation.DELETE_FROM_CART,
            )

    @monitor_performance("get_user_info")
    async def get_user_info(self) -> APIResponse:
        """
        Get user information

        Based on demo/getme.py - Uses GET request to user/getme endpoint
        """
        try:
            config = await self._get_api_config()
            if not config:
                return APIResponse(
                    success=False,
                    error="API configuration not available",
                    operation=APIOperation.GET_USER_INFO,
                )

            # Build request components
            url = f"{config.base_url}/user/getme"
            headers = self._build_headers(config, APIOperation.GET_USER_INFO)
            cookies = self._build_cookies(config)

            logger.info("Getting user information")

            # Make GET request
            return await self._make_request(
                method="GET",
                url=url,
                headers=headers,
                cookies=cookies,
                operation=APIOperation.GET_USER_INFO,
            )

        except Exception as e:
            logger.error(f"Error in get_user_info: {e}")
            return APIResponse(
                success=False,
                error=f"Get user info operation failed: {str(e)}",
                operation=APIOperation.GET_USER_INFO,
            )

    # Utility methods for easier integration

    async def is_authenticated(self) -> bool:
        """Check if the current session is authenticated"""
        try:
            response = await self.get_user_info()
            return response.success and response.data and "user" in response.data
        except Exception as e:
            logger.error(f"Error checking authentication: {e}")
            return False

    async def get_cart_total_price(self) -> Optional[float]:
        """Get the total price of items in cart"""
        try:
            response = await self.view_cart()
            if response.success and response.data:
                return response.data.get("totalCartPrice")
            return None
        except Exception as e:
            logger.error(f"Error getting cart total price: {e}")
            return None

    async def get_cart_item_count(self) -> int:
        """Get the number of items in cart"""
        try:
            response = await self.view_cart()
            if response.success and response.data:
                data_items = response.data.get("data", [])
                return len(data_items) if isinstance(data_items, list) else 0
            return 0
        except Exception as e:
            logger.error(f"Error getting cart item count: {e}")
            return 0

    async def clear_configuration_cache(self):
        """Clear the configuration cache to force reload"""
        self._config_cache = None
        self._cache_expiry = None
        logger.info("Configuration cache cleared")

    def get_supported_operations(self) -> List[APIOperation]:
        """Get list of supported API operations"""
        return list(APIOperation)

    async def ensure_cart_if_empty(
        self,
        item_ids: List[int],
        product_table_name: str = "Cards",
        verify: bool = True,
        delay_between: float = 0.1,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Ensure the external cart has items. If it's empty, add provided item_ids.

        Returns a summary dict:
          {
            'success': bool,
            'performed': bool,         # whether add operations were performed
            'added': int,              # number of successful add operations
            'failed': List[dict],      # details of failed additions
            'present_before': int,     # count of items before population
            'present_after': int,      # count of items after population (if verify)
            'details': str             # optional message
          }
        """
        try:
            # Helper to normalize cart items list from various response shapes
            def _extract_cart_items(data: Any) -> List[Dict[str, Any]]:
                if isinstance(data, dict):
                    if isinstance(data.get("data"), list):
                        return data["data"]
                    if isinstance(data.get("cart"), dict) and isinstance(
                        data["cart"].get("items"), list
                    ):
                        return data["cart"]["items"]
                if isinstance(data, list):
                    return data
                return []

            # Check current cart state
            view_resp = await self.view_cart()
            logger.info("Viewing cart contents")
            before_items = _extract_cart_items(getattr(view_resp, "data", None))
            present_before = len(before_items)

            if present_before > 0:
                return {
                    "success": True,
                    "performed": False,
                    "added": 0,
                    "failed": [],
                    "present_before": present_before,
                    "present_after": present_before,
                    "details": "Cart already had items; no action taken",
                }

            # Populate the cart
            added = 0
            failed: List[Dict[str, Any]] = []
            for item_id in item_ids:
                try:
                    add_resp = await self.add_to_cart(int(item_id), product_table_name)
                except Exception as e:
                    failed.append({"id": item_id, "error": str(e)})
                    continue

                if getattr(add_resp, "success", False):
                    added += 1
                else:
                    failed.append(
                        {
                            "id": item_id,
                            "error": getattr(add_resp, "error", None)
                            or (getattr(add_resp, "data", {}) or {}).get("message")
                            or "unknown",
                        }
                    )
                if delay_between and delay_between > 0:
                    await asyncio.sleep(delay_between)

            present_after = 0
            if verify:
                after_resp = await self.view_cart()
                after_items = _extract_cart_items(getattr(after_resp, "data", None))
                present_after = len(after_items)

            success = added > 0 and len(failed) == 0
            return {
                "success": success,
                "performed": True,
                "added": added,
                "failed": failed,
                "present_before": present_before,
                "present_after": present_after if verify else present_before + added,
                "details": (
                    "Cart populated successfully"
                    if success
                    else "One or more items failed to add"
                ),
            }

        except Exception as e:
            logger.error(f"Error ensuring cart if empty: {e}")
            return {
                "success": False,
                "performed": False,
                "added": 0,
                "failed": [{"error": str(e)}],
                "present_before": 0,
                "present_after": 0,
                "details": "Exception occurred while populating cart",
            }


# Global service instance
_external_api_service: Optional[ExternalAPIService] = None


def get_external_api_service() -> ExternalAPIService:
    """Get the global external API service instance"""
    global _external_api_service
    if _external_api_service is None:
        _external_api_service = ExternalAPIService()
    return _external_api_service


async def close_external_api_service():
    """Close the global external API service instance"""
    global _external_api_service
    if _external_api_service is not None:
        await _external_api_service.close()
        _external_api_service = None
