"""
Checkout Queue Service for managing serialized checkout processing
Ensures proper isolation between users while using a shared website account

Key fixes vs. original:
- Store datetimes as real datetime objects in DB (not ISO strings) so <PERSON><PERSON> can sort/compare.
- Make idempotency key STABLE (removed timestamps); derived from user_id + deterministic cart snapshot hash.
- Added missing _processing_task attribute; unified worker start/stop APIs to avoid confusion.
- Fixed queue position/avg processing time using datetime objects safely.
- Hardened external cart logic (verification, consistent product_id usage).
- Avoid metadata overwrite on status update; merge instead.
- Safer aiohttp session closing, logging, and typing cleanups.
"""

from __future__ import annotations

import aiohttp
import asyncio
import logging
import uuid
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Tuple
from enum import Enum
import json
import hashlib

from database.connection import get_collection, database_transaction
from models import Purchase, PurchaseStatus
from services.cart_service import CartService
from services.user_service import UserService
from services.api_config_service import get_api_config_service, APIConfiguration
from services.external_api_service import get_external_api_service
from utils.api_logging import get_api_logger, LogLevel
from config.settings import get_settings

logger = logging.getLogger(__name__)
api_logger = get_api_logger("checkout_queue_service", LogLevel.DEBUG)


class CheckoutJobStatus(str, Enum):
    """Checkout job status enumeration"""

    QUEUED = "queued"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class CheckoutJob:
    """Represents a checkout job in the queue"""

    def __init__(
        self,
        user_id: str,
        cart_snapshot: Dict[str, Any],
        job_id: str | None = None,
        idempotency_key: str | None = None,
        created_at: datetime | None = None,
        status: CheckoutJobStatus = CheckoutJobStatus.QUEUED,
        metadata: Dict[str, Any] | None = None,
    ):
        self.job_id = job_id or str(uuid.uuid4())
        self.user_id = user_id
        self.cart_snapshot = cart_snapshot
        self.created_at = created_at or datetime.now(timezone.utc)  # aware UTC
        self.status = status
        self.metadata = metadata or {}
        self.attempts = 0
        self.last_error: Optional[str] = None
        self.completed_at: Optional[datetime] = None
        self.idempotency_key = idempotency_key or self._generate_idempotency_key()

    # ---- Helpers -----------------------------------------------------------------
    def _stable_cart_fingerprint(self) -> str:
        """Deterministic hash of cart contents (ignore volatile fields)."""

        def sanitize(obj: Any) -> Any:
            if isinstance(obj, dict):
                drop_keys = {"snapshot_timestamp", "updated_at", "created_at", "_id"}
                return {
                    k: sanitize(v) for k, v in sorted(obj.items()) if k not in drop_keys
                }
            if isinstance(obj, list):
                if obj and isinstance(obj[0], dict) and "card_id" in obj[0]:
                    normalized = [
                        {
                            kk: sanitize(vv)
                            for kk, vv in sorted(item.items())
                            if kk not in {"_id"}
                        }
                        for item in obj
                    ]
                    normalized.sort(
                        key=lambda x: (str(x.get("card_id")), int(x.get("quantity", 1)))
                    )
                    return normalized
                return [sanitize(v) for v in obj]
            return obj

        stable = {
            "items": sanitize(self.cart_snapshot.get("items", [])),
            "total_amount": self.cart_snapshot.get("total_amount", 0.0),
            "total_items": self.cart_snapshot.get("total_items", 0),
            "user_id": self.user_id,
        }
        return hashlib.sha256(
            json.dumps(stable, sort_keys=True, default=str).encode()
        ).hexdigest()

    def _generate_idempotency_key(self) -> str:
        """Stable idempotency key (no timestamps!)."""
        return self._stable_cart_fingerprint()

    def to_dict(self) -> Dict[str, Any]:
        """Convert job to dict for storage (keep datetimes as datetime objects)."""
        return {
            "job_id": self.job_id,
            "user_id": self.user_id,
            "cart_snapshot": self.cart_snapshot,
            "idempotency_key": self.idempotency_key,
            "created_at": self.created_at,
            "status": self.status.value,
            "metadata": self.metadata,
            "attempts": self.attempts,
            "last_error": self.last_error,
            "completed_at": self.completed_at,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "CheckoutJob":
        """Create job from dictionary (accepts datetime or ISO string)."""

        def to_dt(value: Any) -> datetime | None:
            if value is None:
                return None
            if isinstance(value, datetime):
                return value if value.tzinfo else value.replace(tzinfo=timezone.utc)
            if isinstance(value, str):
                return datetime.fromisoformat(value.replace("Z", "+00:00"))
            return None

        job = cls(
            user_id=data["user_id"],
            cart_snapshot=data["cart_snapshot"],
            job_id=data["job_id"],
            idempotency_key=data.get("idempotency_key"),
            created_at=to_dt(data.get("created_at")) or datetime.now(timezone.utc),
            status=CheckoutJobStatus(data["status"]),
            metadata=data.get("metadata", {}),
        )
        job.attempts = data.get("attempts", 0)
        job.last_error = data.get("last_error")
        job.completed_at = to_dt(data.get("completed_at"))
        return job


class CheckoutQueueService:
    """Service for managing checkout queue and processing"""

    def __init__(self):
        self.settings = get_settings()
        self.jobs_collection = get_collection("checkout_jobs")
        self.cart_service = CartService()
        self.user_service = UserService()
        self.api_config_service = get_api_config_service()
        self.external_api_service = get_external_api_service()

        # Queue configuration
        self.job_timeout = 300  # per job timeout
        self.queue_check_interval = 1  # seconds

        # Processing state
        self._processing_lock = asyncio.Lock()
        self._is_processing = False
        self._worker_task: Optional[asyncio.Task] = None
        self._processing_task: Optional[asyncio.Task] = (
            None  # back-compat alias storage
        )

        # External API configuration cache
        self._api_config: Optional[APIConfiguration] = None
        self._config_last_loaded: Optional[datetime] = None
        self._config_cache_ttl = timedelta(minutes=5)

        # Backward-compatible attrs for tests/tools
        self.external_api_url: str | None = None
        self.external_cart_url: str | None = None
        self.external_checkout_url: str | None = None
        self._external_headers: Dict[str, str] = {}
        self._external_cookies: Dict[str, str] = {}

    # ---- Logging helpers --------------------------------------------------------
    def _log_api_response(self, operation: str, response: Any) -> None:
        """Log details from an APIResponse-like object in a consistent way."""
        try:
            status = getattr(response, "status_code", None)
            success = getattr(response, "success", None)
            data = getattr(response, "data", None)
            raw = getattr(response, "raw_response", None)
            logger.info(
                f"API [{operation}] -> status={status}, success={success}, "
                f"data_type={type(data).__name__ if data is not None else 'None'}"
            )
            # Include a truncated view of data/raw for quick diagnostics
            if isinstance(data, (dict, list)):
                try:
                    import json as _json

                    logger.info(
                        f"API [{operation}] data: "
                        f"{_json.dumps(data)[:1000]}{'...' if len(_json.dumps(data))>1000 else ''}"
                    )
                except Exception:
                    logger.info(f"API [{operation}] data (repr): {repr(data)[:1000]}")
            elif data is not None:
                logger.info(f"API [{operation}] data (str): {str(data)[:1000]}")
            if raw:
                logger.info(
                    f"API [{operation}] raw: {raw[:1000]}{'...' if len(raw)>1000 else ''}"
                )
        except Exception as e:
            logger.warning(f"Failed to log API response for {operation}: {e}")

    # ---- API configuration -------------------------------------------------------
    async def _get_api_config(self) -> Optional[APIConfiguration]:
        """Get external API configuration with caching."""
        try:
            now = datetime.now(timezone.utc)
            if (
                self._api_config is None
                or self._config_last_loaded is None
                or now - self._config_last_loaded > self._config_cache_ttl
            ):
                self._api_config = await self.api_config_service.get_api_config(
                    "external_cart"
                )
                self._config_last_loaded = now

                if self._api_config:
                    logger.debug(
                        f"Loaded API configuration from {self._api_config.source.value}"
                    )
                    self.external_api_url = self._api_config.base_url
                    try:
                        self.external_cart_url = self._api_config.endpoints.get("cart_view").url  # type: ignore[union-attr]
                    except Exception:
                        self.external_cart_url = None
                    try:
                        self.external_checkout_url = self._api_config.endpoints.get("checkout").url  # type: ignore[union-attr]
                    except Exception:
                        self.external_checkout_url = None
                    self._external_headers = dict(
                        self._api_config.credentials.headers or {}
                    )
                    self._external_cookies = dict(
                        self._api_config.credentials.session_cookies or {}
                    )
                else:
                    logger.warning("No external cart API configuration found")
            return self._api_config
        except Exception as e:
            logger.error(f"Error getting API configuration: {e}")
            return None

    async def _get_api_endpoint_url(self, endpoint_name: str) -> Optional[str]:
        config = await self._get_api_config()
        if not config or endpoint_name not in config.endpoints:
            logger.error(f"API endpoint '{endpoint_name}' not found in configuration")
            return None
        return config.endpoints[endpoint_name].url

    async def _get_api_endpoint_config(
        self, endpoint_name: str
    ) -> Optional[Dict[str, Any]]:
        config = await self._get_api_config()
        if not config or endpoint_name not in config.endpoints:
            return None
        endpoint = config.endpoints[endpoint_name]
        return {
            "url": endpoint.url,
            "method": endpoint.method,
            "timeout": endpoint.timeout,
            "retry_count": endpoint.retry_count,
            "retry_delays": endpoint.retry_delays,
        }

    # ---- Worker lifecycle --------------------------------------------------------
    async def start_worker(self) -> None:
        """Start the checkout queue worker (preferred API)."""
        if self._worker_task and not self._worker_task.done():
            logger.warning("Checkout worker is already running")
            return
        self._is_processing = True
        self._worker_task = asyncio.create_task(
            self._process_queue(), name="checkout_worker"
        )
        logger.info("Checkout queue worker started")

    async def stop_worker(self) -> None:
        """Stop the checkout queue worker."""
        self._is_processing = False
        if self._worker_task:
            self._worker_task.cancel()
            try:
                await self._worker_task
            except asyncio.CancelledError:
                pass
            finally:
                self._worker_task = None
        logger.info("Checkout queue worker stopped")

    # Back-compat aliases
    async def start_processing(self) -> None:
        await self.start_worker()

    async def stop_processing(self) -> None:
        await self.stop_worker()

    # ---- Public API --------------------------------------------------------------
    async def queue_checkout(
        self, user_id: str, telegram_user_id: int
    ) -> Tuple[bool, str, Optional[str]]:
        """
        Queue a checkout job for processing

        Returns: Tuple of (success, message, job_id)
        """
        try:
            cart_contents = await self.cart_service.get_cart_contents(user_id)
            if cart_contents.get("is_empty", True):
                return False, "Cart is empty", None

            wallet = await self.user_service.get_wallet(user_id)
            total_amount = float(cart_contents.get("total_amount", 0.0))
            if not wallet or not wallet.can_spend(total_amount):
                return (
                    False,
                    f"Insufficient funds. Need ${total_amount}, have ${wallet.balance if wallet else 0}",
                    None,
                )

            # Snapshot serializer (OK to keep ISO strings in snapshot)
            def serialize_mongo_dict(data: Any) -> Any:
                if isinstance(data, dict):
                    return {
                        key: serialize_mongo_dict(value) for key, value in data.items()
                    }
                if isinstance(data, list):
                    return [serialize_mongo_dict(item) for item in data]
                if isinstance(data, datetime):
                    return data.astimezone(timezone.utc).isoformat()
                return data

            cart_snapshot = {
                "cart": (
                    serialize_mongo_dict(cart_contents["cart"].to_mongo())
                    if cart_contents["cart"]
                    else None
                ),
                "items": [
                    serialize_mongo_dict(item.to_mongo())
                    for item in cart_contents["items"]
                ],
                "total_amount": total_amount,
                "total_items": cart_contents.get("total_items", 0),
                "snapshot_timestamp": datetime.now(timezone.utc).isoformat(),
                "telegram_user_id": telegram_user_id,
            }

            # Idempotency check using stable fingerprint of cart
            existing_job = await self._find_existing_job(user_id, cart_snapshot)
            if existing_job:
                return (
                    True,
                    f"Order already queued (Job #{existing_job.job_id[:8]})",
                    existing_job.job_id,
                )

            job = CheckoutJob(
                user_id=user_id,
                cart_snapshot=cart_snapshot,
                metadata={
                    "telegram_user_id": telegram_user_id,
                    "estimated_processing_time": await self._estimate_processing_time(),
                },
            )

            await self.jobs_collection.insert_one(job.to_dict())

            queue_position = await self._get_queue_position(job.job_id)
            estimated_wait = queue_position * 30  # seconds

            logger.info(f"Queued checkout job {job.job_id} for user {user_id}")
            return (
                True,
                (
                    "⏳ Order queued for processing!\n"
                    f"📍 Position: #{queue_position} in line\n"
                    f"⏱️ Estimated wait: {estimated_wait // 60}m {estimated_wait % 60}s\n"
                    f"🆔 Job ID: {job.job_id[:8]}"
                ),
                job.job_id,
            )
        except Exception as e:
            logger.error(f"Error queuing checkout for user {user_id}: {e}")
            return False, f"Failed to queue checkout: {str(e)}", None

    async def cancel_checkout(self, user_id: str, job_id: str) -> Tuple[bool, str]:
        try:
            job_doc = await self.jobs_collection.find_one(
                {
                    "job_id": job_id,
                    "user_id": user_id,
                    "status": {"$in": [CheckoutJobStatus.QUEUED.value]},
                }
            )
            if not job_doc:
                return False, "Job not found or cannot be cancelled"

            await self.jobs_collection.update_one(
                {"job_id": job_id},
                {
                    "$set": {
                        "status": CheckoutJobStatus.CANCELLED.value,
                        "completed_at": datetime.now(timezone.utc),
                    }
                },
            )
            logger.info(f"Cancelled checkout job {job_id} for user {user_id}")
            return True, f"✅ Order #{job_id[:8]} has been cancelled"
        except Exception as e:
            logger.error(f"Error cancelling checkout job {job_id}: {e}")
            return False, f"Failed to cancel order: {str(e)}"

    async def get_job_status(self, job_id: str) -> Optional[CheckoutJob]:
        try:
            job_doc = await self.jobs_collection.find_one({"job_id": job_id})
            return CheckoutJob.from_dict(job_doc) if job_doc else None
        except Exception as e:
            logger.error(f"Error getting job status for {job_id}: {e}")
            return None

    async def get_user_jobs(self, user_id: str, limit: int = 10) -> List[CheckoutJob]:
        try:
            jobs_docs = (
                await self.jobs_collection.find({"user_id": user_id})
                .sort("created_at", -1)
                .limit(limit)
                .to_list(None)
            )
            return [CheckoutJob.from_dict(doc) for doc in jobs_docs]
        except Exception as e:
            logger.error(f"Error getting user jobs for {user_id}: {e}")
            return []

    # ---- Queue processing --------------------------------------------------------
    async def _process_queue(self) -> None:
        logger.info("Starting checkout queue processing")
        while self._is_processing:
            try:
                job = await self._get_next_job()
                if job:
                    await self._process_job(job)
                else:
                    await asyncio.sleep(self.queue_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in queue processing loop: {e}")
                await asyncio.sleep(5)
        logger.info("Checkout queue processing stopped")

    async def _get_next_job(self) -> Optional[CheckoutJob]:
        try:
            cursor = (
                self.jobs_collection.find({"status": CheckoutJobStatus.QUEUED.value})
                .sort("created_at", 1)
                .limit(1)
            )
            jobs_list = await cursor.to_list(1)
            return CheckoutJob.from_dict(jobs_list[0]) if jobs_list else None
        except Exception as e:
            logger.error(f"Error getting next job: {e}")
            return None

    async def _process_job(self, job: CheckoutJob) -> None:
        logger.info(f"Attempting to acquire checkout lock for job {job.job_id}")
        async with self._processing_lock:
            lock_acquired_time = datetime.now(timezone.utc)
            logger.info(
                f"🔒 Checkout lock acquired for job {job.job_id} (user: {job.user_id}) at {lock_acquired_time.isoformat()}"
            )
            try:
                logger.info(
                    (
                        f"Processing checkout job {job.job_id} for user {job.user_id} - "
                        f"Items: {len(job.cart_snapshot.get('items', []))}, "
                        f"Total: ${job.cart_snapshot.get('total_amount', 0.0)}"
                    )
                )
                await self._update_job_status(job.job_id, CheckoutJobStatus.PROCESSING)
                await self._notify_user(job, "🔄 Processing your order...")

                # Single-attempt checkout (no overall retries)
                success, message, result_data = await self._execute_checkout(job)
                if success:
                    await self._update_job_status(
                        job.job_id, CheckoutJobStatus.COMPLETED, metadata=result_data
                    )
                    await self._notify_user(job, f"✅ {message}", result_data)
                    logger.info(
                        f"✅ Successfully completed job {job.job_id} - Order processed successfully"
                    )
                else:
                    await self._update_job_status(
                        job.job_id, CheckoutJobStatus.FAILED, error=message
                    )
                    await self._notify_user(job, f"❌ {message}")
                    logger.error(f"❌ Failed to complete job {job.job_id}: {message}")
            except Exception as e:
                logger.error(f"💥 Critical error processing job {job.job_id}: {e}")
                await self._update_job_status(
                    job.job_id, CheckoutJobStatus.FAILED, error=str(e)
                )
                await self._notify_user(job, f"❌ Order processing failed: {str(e)}")
            finally:
                lock_released_time = datetime.now(timezone.utc)
                processing_duration = (
                    lock_released_time - lock_acquired_time
                ).total_seconds()
                logger.info(
                    f"🔓 Checkout lock released for job {job.job_id} at {lock_released_time.isoformat()} "
                    f"(held for {processing_duration:.2f}s)"
                )

    # Removed retry wrapper: checkout is executed once per job

    async def _execute_checkout(
        self, job: CheckoutJob
    ) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        import aiohttp

        session: aiohttp.ClientSession | None = None
        checkout_step = "initialization"
        try:
            # Step 1: Validate cart snapshot
            checkout_step = "cart_validation"
            cart_snapshot = job.cart_snapshot
            items = cart_snapshot.get("items", [])
            total_amount = float(cart_snapshot.get("total_amount", 0.0))
            if not items or total_amount <= 0:
                logger.warning(
                    f"Invalid cart snapshot for job {job.job_id}: items={len(items)}, total={total_amount}"
                )
                return False, "Invalid cart snapshot", None

            # Step 2: Validate funds
            checkout_step = "funds_validation"
            wallet = await self.user_service.get_wallet(job.user_id)
            if not wallet or not wallet.can_spend(total_amount):
                logger.warning(
                    f"Insufficient funds for job {job.job_id}: need=${total_amount}, have=${wallet.balance if wallet else 0}"
                )
                return (
                    False,
                    f"Insufficient funds. Need ${total_amount}, have ${wallet.balance if wallet else 0}",
                    None,
                )

            # Step 3: External API session
            checkout_step = "session_creation"
            session = await self._get_external_session()
            if not session:
                logger.error(
                    f"Failed to create external API session for job {job.job_id}"
                )
                return False, "Failed to create external API session", None

            # Step 3b: Proactive authentication check to avoid silent failures
            try:
                is_auth = await self.external_api_service.is_authenticated()
            except Exception as auth_e:
                logger.error(f"External API auth check failed: {auth_e}")
                is_auth = False
            if not is_auth:
                await self._safe_close_session(session)
                return (
                    False,
                    "External API authentication failed. Please configure 'api1_external_cart' credentials in Admin > APIs.",
                    None,
                )

            # Step 4: Clear external cart
            checkout_step = "cart_clearing"
            logger.info(f"Clearing external cart for user {job.user_id}")
            # Single attempt cart clear (no retries)
            clear_success = await self._clear_external_cart(session)
            if clear_success:
                logger.info("✅ Cart clearing successful")
            if not clear_success:
                await self._safe_close_session(session)
                return (
                    False,
                    "Failed to clear external cart - isolation compromised",
                    None,
                )

            # Step 5: Populate cart
            checkout_step = "cart_population"
            logger.info(
                f"Populating external cart with {len(items)} items for user {job.user_id}"
            )
            populate_success = await self._populate_external_cart(session, items)
            if not populate_success:
                try:
                    external_items_debug = await self._get_external_cart_items(session)
                    try:
                        debug_ids = [
                            str(
                                it.get("product_id")
                                or it.get("card_id")
                                or it.get("productId")
                                or it.get("id")
                                or ""
                            )
                            for it in external_items_debug
                        ]
                    except Exception:
                        debug_ids = []
                    logger.error(
                        f"🔍 External cart has {len(external_items_debug)} items after failed population: {debug_ids}"
                    )

                    # Fallback: If cart is completely empty, attempt population via ExternalAPIService helper
                    if len(external_items_debug) == 0:
                        try:
                            fallback_ids: list[int] = []
                            for it in items:
                                cid = it.get("card_id")
                                if cid is not None:
                                    try:
                                        fallback_ids.append(int(cid))
                                    except Exception:
                                        continue
                            if fallback_ids:
                                logger.info(
                                    f"🛠️ Attempting fallback population via ensure_cart_if_empty for {len(fallback_ids)} items"
                                )
                                from services.external_api_service import (
                                    get_external_api_service,
                                )

                                ext = get_external_api_service()
                                ensure_result = await ext.ensure_cart_if_empty(
                                    fallback_ids, product_table_name="Cards", verify=True
                                )
                                logger.info(
                                    f"Fallback population result: success={ensure_result.get('success')}, added={ensure_result.get('added')}, present_after={ensure_result.get('present_after')}"
                                )
                                if ensure_result.get("success") and ensure_result.get(
                                    "present_after", 0
                                ) > 0:
                                    populate_success = True
                        except Exception as fb_e:
                            logger.error(
                                f"Fallback ensure_cart_if_empty failed: {fb_e}"
                            )
                except Exception as debug_e:
                    logger.error(
                        f"   Failed to get external cart for debugging: {debug_e}"
                    )
                if not populate_success:
                    await self._safe_close_session(session)
                    return False, "Failed to populate external cart with user items", None

            # Immediate verification
            try:
                immediate_check_items = await self._get_external_cart_items(session)
                for expected_item in items:
                    expected_card_id = str(expected_item.get("card_id", ""))
                    expected_qty = int(expected_item.get("quantity", 1))
                    actual_count = sum(
                        1
                        for it in immediate_check_items
                        if str(it.get("product_id", "")) == expected_card_id
                    )
                    if actual_count < expected_qty:
                        await self._safe_close_session(session)
                        return (
                            False,
                            f"Cart population verification failed: Card {expected_card_id} expected {expected_qty}, found {actual_count}",
                            None,
                        )
            except Exception as immediate_check_e:
                logger.error(
                    f"Immediate post-population check failed: {immediate_check_e}"
                )

            # Step 6: Validate external cart
            checkout_step = "cart_validation_external"
            await asyncio.sleep(0.5)
            validation_result = await self._validate_cart_items(session, items)
            if not validation_result["valid"]:
                await self._safe_close_session(session)
                return (
                    False,
                    f"Cart validation failed: {validation_result['message']}",
                    None,
                )

            # Step 7: External checkout
            checkout_step = "external_checkout"
            checkout_result = await self._execute_external_checkout(session)
            await self._safe_close_session(session)
            session = None
            if not checkout_result.get("success"):
                return (
                    False,
                    checkout_result.get("message", "External checkout failed"),
                    None,
                )

            # Step 8: Local processing
            success, message, local_result = await self._process_successful_checkout(
                job.user_id, cart_snapshot, checkout_result
            )
            if success:
                result_data = {
                    "external_order_id": checkout_result.get("order_id"),
                    "transaction_id": (
                        local_result.get("transaction_id") if local_result else None
                    ),
                    "total_amount": total_amount,
                    "items_purchased": len(items),
                    "remaining_balance": (
                        local_result.get("remaining_balance") if local_result else None
                    ),
                    "checkout_timestamp": datetime.now(timezone.utc).isoformat(),
                    "validation_warnings": validation_result.get("warnings", []),
                }
                return True, message, result_data
            return False, message, None
        except Exception as e:
            logger.error(
                f"Error executing checkout for job {job.job_id} at step '{checkout_step}': {e}"
            )
            if session:
                await self._safe_close_session(session)
            return (
                False,
                f"Checkout execution failed at {checkout_step}: {str(e)}",
                None,
            )

    # ---- External API helpers ----------------------------------------------------
    async def _safe_close_session(self, session: "aiohttp.ClientSession") -> None:
        try:
            if session and not session.closed:
                await session.close()
                logger.debug("External API session closed successfully")
        except Exception as e:
            logger.warning(f"Error closing external API session: {e}")

    async def _get_external_session(self) -> Optional["aiohttp.ClientSession"]:
        try:
            import aiohttp

            config = await self._get_api_config()
            if not config:
                logger.error("No external API configuration available")
                return None
            if not getattr(config, "enabled", True):
                logger.error("External API configuration is disabled")
                return None
            timeout = aiohttp.ClientTimeout(total=60, connect=10)
            connector = aiohttp.TCPConnector(limit=10, limit_per_host=5)
            # Inject Authorization header into session if token exists
            sess_headers = dict(config.credentials.headers or {})
            try:
                token = (config.credentials.login_token or "").strip()
            except Exception:
                token = ""
            if token and "Authorization" not in sess_headers:
                sess_headers["Authorization"] = f"Bearer {token}"
            # Ensure loginToken cookie exists for endpoints that require it
            sess_cookies = dict(config.credentials.session_cookies or {})
            if token and "loginToken" not in sess_cookies:
                sess_cookies["loginToken"] = token

            session = aiohttp.ClientSession(
                timeout=timeout,
                connector=connector,
                headers=sess_headers,
                cookies=sess_cookies,
            )
            return session
        except Exception as e:
            logger.error(f"Error creating external session: {e}")
            return None

    async def _clear_external_cart(self, session: "aiohttp.ClientSession") -> bool:
        try:
            logger.info("🧹 Starting external cart clearing process...")
            cart_items = await self._get_external_cart_items(session)
            if not cart_items:
                logger.info("✅ External cart is already empty")
                return True
            removed_count = 0
            failed_removals: list[dict[str, Any]] = []
            for i, item in enumerate(cart_items, 1):
                item_id = item.get("_id") or item.get("id")
                item_name = (
                    item.get("name")
                    or f"{item.get('brand', 'Unknown')} {item.get('bin', 'Card')}"
                    or f"Card {item_id}"
                )
                if item_id is None:
                    logger.warning(f"⚠️ Item {i} has no ID, skipping removal")
                    continue
                logger.debug(
                    f"🗑️ Removing item {i}/{len(cart_items)}: {item_name} (ID: {item_id})"
                )
                success = await self._remove_external_cart_item(session, item_id)
                if success:
                    removed_count += 1
                else:
                    failed_removals.append({"id": item_id, "name": item_name})
            remaining_items = await self._get_external_cart_items(session)
            if remaining_items:
                logger.error(
                    f"❌ Cart clearing incomplete: {len(remaining_items)} items remain after removal attempt"
                )
                for item in remaining_items:
                    iid = item.get("_id") or item.get("id", "No ID")
                    logger.error(f"   - Remaining item ID: {iid}")
                return False
            if failed_removals:
                logger.warning(
                    f"⚠️ {len(failed_removals)} items failed to remove but cart appears empty"
                )
            logger.info(
                f"✅ Successfully cleared external cart - {removed_count} items removed, 0 remaining"
            )
            return True
        except Exception as e:
            logger.error(f"💥 Critical error clearing external cart: {e}")
            return False

    async def _get_external_cart_items(
        self, session: "aiohttp.ClientSession" | None = None
    ) -> List[Dict[str, Any]]:
        try:
            response = await self.external_api_service.view_cart()
            # Always log the API response for diagnostics
            self._log_api_response("view_cart", response)
            data = getattr(response, "data", None)
            if data is not None:
                # Be tolerant: some servers may omit an explicit success flag but still return items
                if isinstance(data, dict):
                    if isinstance(data.get("data"), list):
                        return data["data"]
                    if isinstance(data.get("cart"), dict) and isinstance(
                        data["cart"].get("items"), list
                    ):
                        return data["cart"]["items"]
                if isinstance(data, list):
                    return data
            else:
                logger.warning(
                    f"Failed to get external cart items: {getattr(response, 'error', 'unknown error')}"
                )
            return []
        except Exception as e:
            logger.error(f"Error getting external cart items: {e}")
            return []

    async def _remove_external_cart_item(
        self, session: "aiohttp.ClientSession", item_id: Any
    ) -> bool:
        try:
            logger.debug(f"Removing cart item {item_id} from external cart")
            try:
                numeric_id = int(item_id)
                resp = await self.external_api_service.delete_from_cart(numeric_id)
            except (ValueError, TypeError):
                resp = await self.external_api_service.delete_from_cart(item_id)
            # Log delete response for visibility
            self._log_api_response("delete_from_cart", resp)
            if getattr(resp, "success", False):
                return True
            err = str(getattr(resp, "error", ""))
            if "not found" in err.lower() or "404" in err:
                return True
            logger.warning(
                f"Failed to remove cart item {item_id} from external cart: {err}"
            )
            return False
        except Exception as e:
            logger.error(f"Error removing external cart item {item_id}: {e}")
            return False

    async def _add_to_external_cart(
        self, session: "aiohttp.ClientSession", card_id: int | str
    ) -> bool:
        try:
            logger.info(f"🔄 Attempting to add card {card_id} to external cart")

            # ENHANCED LOGGING: Pre-request cart state
            logger.debug(
                f"📋 PRE-ADD: Checking external cart state before adding card {card_id}"
            )
            pre_add_items = await self._get_external_cart_items(session)
            logger.debug(f"   Pre-add cart has {len(pre_add_items)} items")
            try:
                pre_ids = [
                    str(
                        it.get("product_id")
                        or it.get("card_id")
                        or it.get("productId")
                        or it.get("id")
                        or ""
                    )
                    for it in pre_add_items
                ]
            except Exception:
                pre_ids = []
            logger.debug(f"   Pre-add product_ids: {pre_ids}")

            # Make the API call
            logger.info(f"📡 Making add_to_cart API call for card {card_id}")
            response = await self.external_api_service.add_to_cart(
                int(card_id), "Cards"
            )

            # ENHANCED LOGGING: Complete API response details
            logger.info(f"📡 Add to cart API response for card {card_id}:")
            self._log_api_response("add_to_cart", response)

            # ENHANCED LOGGING: Success determination analysis
            success_value = getattr(response, "success", None)
            logger.debug(f"🔍 Success determination for card {card_id}:")
            logger.debug(
                f"   response.success = {success_value} (type: {type(success_value)})"
            )
            logger.debug(
                f"   Evaluating: getattr(response, 'success', False) = {getattr(response, 'success', False)}"
            )
            if getattr(response, "success", False):
                logger.info(
                    f"✅ API reported SUCCESS for card {card_id} - proceeding with verification"
                )

                # ENHANCED LOGGING: Post-request cart state with timing
                import time

                verification_start = time.time()
                logger.debug(
                    f"📋 POST-ADD: Checking external cart state after adding card {card_id}"
                )

                # Add small delay to allow API to process
                await asyncio.sleep(0.75)

                # Single verification check (no multi-attempt loop)
                external_items = await self._get_external_cart_items(session)
                verification_time = time.time() - verification_start

                logger.debug(
                    f"   Post-add cart has {len(external_items)} items (verification took {verification_time:.2f}s)"
                )
                try:
                    post_ids = [
                        str(
                            it.get("product_id")
                            or it.get("card_id")
                            or it.get("productId")
                            or it.get("id")
                            or ""
                        )
                        for it in external_items
                    ]
                except Exception:
                    post_ids = []
                logger.debug(f"   Post-add product_ids: {post_ids}")

                # Check if card was actually added (robust key matching)
                def _extract_product_id(it: dict) -> str:
                    # External API responses have varied over time; try common keys
                    return str(
                        it.get("product_id")
                        or it.get("card_id")
                        or it.get("productId")
                        or it.get("id")
                        or ""
                    )

                normalized_card_id = str(card_id)
                found_items = [
                    item for item in external_items if _extract_product_id(item) == normalized_card_id
                ]
                found = len(found_items) > 0

                logger.debug(f"🔍 Verification results for card {card_id}:")
                logger.debug(f"   Looking for product_id: {card_id}")
                logger.debug(f"   Found {len(found_items)} matching items")
                logger.debug(f"   Match found: {found}")

                if found:
                    logger.info(
                        f"✅ VERIFICATION SUCCESS: Card {card_id} found in external cart"
                    )
                    for i, item in enumerate(found_items):
                        logger.debug(
                            f"   Match {i+1}: Cart ID={item.get('_id')}, Product ID={item.get('product_id')}"
                        )
                    return True
                else:
                    logger.error(
                        f"❌ VERIFICATION FAILED: Card {card_id} not found in external cart after API success"
                    )
                    logger.error(f"   Expected product_id: {card_id}")
                    try:
                        available_ids = [_extract_product_id(item) for item in external_items]
                    except Exception:
                        available_ids = []
                    logger.error(f"   Available product_ids: {available_ids}")
                    logger.error(f"   Full external cart items: {external_items}")

                    # Additional debugging: Check for partial matches
                    partial_matches = [
                        item
                        for item in external_items
                        if str(card_id) in str(item.get("product_id", ""))
                    ]
                    if partial_matches:
                        logger.error(f"   Partial matches found: {partial_matches}")

                    return False
            else:
                logger.error(f"❌ API reported FAILURE for card {card_id}")

                # ENHANCED LOGGING: Analyze failure response
                msg = ""
                if hasattr(response, "data") and isinstance(response.data, dict):
                    msg = str(response.data.get("message", ""))
                    logger.debug(f"   Failure message: '{msg}'")

                # Handle "already in cart" scenario
                if "already have this product in cart" in msg.lower():
                    logger.warning(
                        f"🔄 Card {card_id} already in cart - verifying presence..."
                    )

                    external_items = await self._get_external_cart_items(session)
                    found_items = [
                        item for item in external_items if _extract_product_id(item) == normalized_card_id
                    ]

                    logger.debug(f"   Already-in-cart verification:")
                    logger.debug(f"   Cart has {len(external_items)} items")
                    logger.debug(f"   Found {len(found_items)} matching card {card_id}")

                    if found_items:
                        logger.info(
                            f"✅ RESOLVED: Card {card_id} already in cart and verified present"
                        )
                        for i, item in enumerate(found_items):
                            logger.debug(
                                f"   Existing item {i+1}: Cart ID={item.get('_id')}, Product ID={item.get('product_id')}"
                            )
                        return True
                    else:
                        logger.error(
                            f"❌ CONFLICT: API says already in cart but item not found"
                        )
                        logger.error(
                            f"   Available product_ids: {[item.get('product_id') for item in external_items]}"
                        )
                        return False
                else:
                    logger.error(
                        f"❌ GENUINE FAILURE: Card {card_id} could not be added"
                    )
                    logger.error(f"   Failure reason: {msg if msg else 'Unknown'}")
                    return False
        except Exception as e:
            logger.error(f"💥 EXCEPTION adding card {card_id} to external cart: {e}")
            return False

    async def _populate_external_cart(
        self, session: "aiohttp.ClientSession", cart_items: List[Dict[str, Any]]
    ) -> bool:
        try:
            total_quantity = sum(int(item.get("quantity", 1)) for item in cart_items)
            logger.info(
                f"🛒 Starting cart population: {len(cart_items)} unique items, {total_quantity} total items"
            )
            failed_items: list[dict[str, Any]] = []
            for idx, item_data in enumerate(cart_items, 1):
                card_id = item_data.get("card_id")
                quantity = int(item_data.get("quantity", 1))
                card_name = item_data.get("card_data", {}).get(
                    "name", f"Card {card_id}"
                )
                if not card_id:
                    logger.warning(
                        f"⚠️ Skipping item {idx} with missing card_id: {item_data}"
                    )
                    failed_items.append(item_data)
                    continue
                logger.info(
                    f"📦 Adding item {idx}/{len(cart_items)}: {card_name} (ID: {card_id}, Qty: {quantity})"
                )

                # ENHANCED LOGGING: Track cart state before adding this item
                logger.debug(
                    f"🔍 PRE-ITEM: Cart state before adding {card_name} (ID: {card_id})"
                )
                pre_item_cart = await self._get_external_cart_items(session)
                logger.debug(
                    f"   Cart has {len(pre_item_cart)} items before adding {card_name}"
                )

                success_count = 0
                for i in range(quantity):
                    logger.debug(
                        f"   🔄 Adding copy {i+1}/{quantity} of card {card_id}"
                    )
                    add_success = await self._add_to_external_cart(session, card_id)

                    if add_success:
                        success_count += 1
                        logger.debug(
                            f"   ✅ Copy {i+1}/{quantity} of card {card_id} added successfully"
                        )
                    else:
                        logger.error(
                            f"   ❌ Copy {i+1}/{quantity} of card {card_id} failed to add"
                        )
                        failed_items.append(
                            {
                                "card_id": card_id,
                                "card_name": card_name,
                                "attempt": i + 1,
                                "quantity": quantity,
                            }
                        )
                if success_count != quantity:
                    logger.warning(
                        f"⚠️ Only added {success_count}/{quantity} copies of {card_name}"
                    )
                await asyncio.sleep(0.1)
            if failed_items:
                logger.error(f"❌ Cart population had failures: {len(failed_items)}")
                for f in failed_items[:5]:
                    logger.error(f"   - Failed: {f}")
                return False
            logger.info("✅ Successfully populated external cart")
            return True
        except Exception as e:
            logger.error(f"💥 Critical error populating external cart: {e}")
            return False

    async def _validate_cart_items(
        self, session: "aiohttp.ClientSession", expected_items: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        try:
            logger.info("Validating external cart contents...")
            external_items = await self._get_external_cart_items(session)

            expected_map: dict[str, int] = {}
            for item in expected_items:
                card_id = str(item.get("card_id", ""))
                qty = int(item.get("quantity", 1))
                if card_id:
                    expected_map[card_id] = expected_map.get(card_id, 0) + qty

            actual_map: dict[str, int] = {}
            for item in external_items:
                card_id = str(
                    item.get("product_id")
                    or item.get("card_id")
                    or item.get("id")
                    or ""
                )
                if card_id:
                    actual_map[card_id] = actual_map.get(card_id, 0) + 1

            errors: list[str] = []
            for cid, exp in expected_map.items():
                act = actual_map.get(cid, 0)
                if act < exp:
                    errors.append(f"Card {cid}: expected {exp}, found {act}")
            for cid, act in actual_map.items():
                exp = expected_map.get(cid, 0)
                if act > exp:
                    errors.append(f"Card {cid}: unexpected {act - exp} extra items")

            if errors:
                msg = "; ".join(errors)
                logger.error(f"Cart validation failed: {msg}")
                return {
                    "valid": False,
                    "message": msg,
                    "expected_items": expected_map,
                    "actual_items": actual_map,
                    "errors": errors,
                }

            stock_validation = await self._validate_stock_and_prices(
                session, expected_items, external_items
            )
            if not stock_validation["valid"]:
                return stock_validation

            return {
                "valid": True,
                "message": "Cart contents validated successfully",
                "expected_items": expected_map,
                "actual_items": actual_map,
                "warnings": stock_validation.get("warnings", []),
            }
        except Exception as e:
            logger.error(f"Error validating cart items: {e}")
            return {"valid": False, "message": f"Validation failed: {str(e)}"}

    async def _validate_stock_and_prices(
        self,
        session: "aiohttp.ClientSession",
        expected_items: List[Dict[str, Any]],
        external_items: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        try:
            warnings: list[str] = []

            price_by_card: dict[str, float] = {}
            for it in expected_items:
                cid = str(it.get("card_id", ""))
                if cid:
                    try:
                        price_by_card[cid] = float(it.get("price_at_add"))
                    except (TypeError, ValueError):
                        pass

            for ext in external_items:
                cid = str(
                    ext.get("product_id") or ext.get("card_id") or ext.get("id") or ""
                )
                if not cid:
                    continue
                if ext.get("out_of_stock") or ext.get("unavailable"):
                    warnings.append(f"Card {cid} appears to be out of stock")
                current_price = (
                    ext.get("price")
                    if ext.get("price") is not None
                    else ext.get("current_price")
                )
                if current_price is not None and cid in price_by_card:
                    try:
                        cp = float(current_price)
                        ep = float(price_by_card[cid])
                        if abs(cp - ep) > 0.01:
                            warnings.append(
                                f"Card {cid} price changed: expected ${ep}, current ${cp}"
                            )
                    except (TypeError, ValueError):
                        pass

            if warnings:
                logger.warning(
                    "Stock/price validation warnings: %s", "; ".join(warnings)
                )
            return {
                "valid": True,
                "message": (
                    "Stock and price validation passed"
                    if not warnings
                    else "Warnings present"
                ),
                "warnings": warnings,
            }
        except Exception as e:
            logger.error(f"Error validating stock and prices: {e}")
            return {
                "valid": False,
                "message": f"Stock/price validation failed: {str(e)}",
            }

    async def _execute_external_checkout(
        self, session: "aiohttp.ClientSession"
    ) -> Dict[str, Any]:
        try:
            import aiohttp  # noqa: F401  # (clarity)

            logger.info("Executing external checkout API call")
            checkout_url = await self._get_api_endpoint_url("checkout")
            if not checkout_url:
                return {"success": False, "message": "Checkout endpoint not configured"}
            config = await self._get_api_config()
            if not config:
                return {"success": False, "message": "API configuration not available"}

            headers = dict(config.credentials.headers or {})
            headers["Referer"] = headers.get(
                "Referer", "https://ronaldo-club.to/store/cart"
            )
            # Ensure Authorization header is present
            try:
                token = (config.credentials.login_token or "").strip()
            except Exception:
                token = ""
            if token and "Authorization" not in headers:
                headers["Authorization"] = f"Bearer {token}"

            # Updated API: GET /cart/checkout (no body)
            # Structured request log
            ctx = api_logger.create_context(user_id=None, operation="checkout")
            api_logger.log_request(
                context=ctx,
                method="GET",
                url=checkout_url,
                headers=headers,
                query_params=None,
                body=None,
                timeout=60.0,
                retry_count=0,
            )
            async with session.get(
                checkout_url, headers=headers, timeout=60
            ) as response:
                status = response.status
                # Log headers and status early
                try:
                    resp_headers = dict(response.headers)
                    logger.info(
                        f"API [checkout] -> status={status}, headers={ {k:v for k,v in list(resp_headers.items())[:10]} }"
                    )
                except Exception:
                    pass
                if status == 200:
                    try:
                        data = await response.json()
                    except Exception:
                        data = None
                    # Log JSON body
                    try:
                        import json as _json

                        logger.info(
                            f"API [checkout] data: "
                            f"{_json.dumps(data)[:1000] if isinstance(data,(dict,list)) else str(data)[:1000]}"
                        )
                    except Exception:
                        pass
                    # Structured response log (success path)
                    try:
                        raw_text = _json.dumps(data) if isinstance(data, (dict, list)) else str(data)
                    except Exception:
                        raw_text = str(data)
                    api_logger.log_response(
                        context=ctx,
                        status_code=status,
                        status_message=response.reason or "OK",
                        headers=resp_headers if 'resp_headers' in locals() else {},
                        body=raw_text,
                        error_type=None,
                        error_message=None,
                    )
                    if isinstance(data, dict) and data.get("success"):
                        order_id = (
                            data.get("order_id")
                            or data.get("id")
                            or f"ext_{uuid.uuid4().hex[:8]}"
                        )
                        return {
                            "success": True,
                            "order_id": order_id,
                            "message": "External checkout completed successfully",
                            "response_data": data,
                        }
                    error_msg = (
                        (data or {}).get("error") if isinstance(data, dict) else None
                    )
                    # Structured response log (200 with failure message)
                    api_logger.log_response(
                        context=ctx,
                        status_code=status,
                        status_message=response.reason or "OK",
                        headers=resp_headers if 'resp_headers' in locals() else {},
                        body=data if isinstance(data, (dict, list)) else (raw_text if 'raw_text' in locals() else None),
                        error_type="api_error",
                        error_message=error_msg or "External checkout failed",
                    )
                    return {
                        "success": False,
                        "message": error_msg or "External checkout failed",
                        "response_data": data,
                    }
                if status == 400:
                    try:
                        error_data = await response.json()
                        error_msg = error_data.get("error", "Invalid checkout request")
                        logger.info(
                            f"API [checkout] 400 body: {error_data}"
                        )
                    except Exception:
                        error_msg = "Invalid checkout request"
                    # Structured response log (400)
                    api_logger.log_response(
                        context=ctx,
                        status_code=status,
                        status_message=response.reason or "Bad Request",
                        headers=resp_headers if 'resp_headers' in locals() else {},
                        body=error_data if 'error_data' in locals() else None,
                        error_type="http_error",
                        error_message=error_msg,
                    )
                    return {
                        "success": False,
                        "message": f"Checkout failed: {error_msg}",
                    }
                if status == 401:
                    try:
                        body = await response.text()
                        logger.info(f"API [checkout] 401 body: {body[:1000]}")
                    except Exception:
                        pass
                    # Structured response log (401)
                    api_logger.log_response(
                        context=ctx,
                        status_code=status,
                        status_message=response.reason or "Unauthorized",
                        headers=resp_headers if 'resp_headers' in locals() else {},
                        body=body if 'body' in locals() else None,
                        error_type="auth_error",
                        error_message="Authentication failed - please check login credentials",
                    )
                    return {
                        "success": False,
                        "message": "Authentication failed - please check login credentials",
                    }
                try:
                    err_text = await response.text()
                except Exception:
                    err_text = ""
                logger.error(f"Checkout API returned status {status}: {err_text}")
                # Structured response log (other errors)
                api_logger.log_response(
                    context=ctx,
                    status_code=status,
                    status_message=response.reason or "Error",
                    headers=resp_headers if 'resp_headers' in locals() else {},
                    body=err_text,
                    error_type="http_error" if status >= 400 else None,
                    error_message=f"Status {status}",
                )
                # Single fallback attempt to legacy endpoint if we got 404/405
                if status in (404, 405):
                    try:
                        base_url = (config.base_url or "https://ronaldo-club.to/api").rstrip("/")
                        fallback_url = f"{base_url}/checkout/"
                        fb_ctx = api_logger.create_context(user_id=None, operation="checkout_fallback")
                        api_logger.log_request(
                            context=fb_ctx,
                            method="POST",
                            url=fallback_url,
                            headers=headers,
                            query_params=None,
                            body=None,
                            timeout=60.0,
                            retry_count=0,
                        )
                        async with session.post(fallback_url, headers=headers, timeout=60) as fb_resp:
                            fb_status = fb_resp.status
                            try:
                                fb_headers = dict(fb_resp.headers)
                            except Exception:
                                fb_headers = {}
                            if fb_status == 200:
                                try:
                                    fb_data = await fb_resp.json()
                                except Exception:
                                    fb_data = None
                                api_logger.log_response(
                                    context=fb_ctx,
                                    status_code=fb_status,
                                    status_message=fb_resp.reason or "OK",
                                    headers=fb_headers,
                                    body=fb_data if isinstance(fb_data, (dict, list)) else None,
                                    error_type=None,
                                    error_message=None,
                                )
                                if isinstance(fb_data, dict) and fb_data.get("success"):
                                    order_id = (
                                        fb_data.get("order_id")
                                        or fb_data.get("id")
                                        or f"ext_{uuid.uuid4().hex[:8]}"
                                    )
                                    return {
                                        "success": True,
                                        "order_id": order_id,
                                        "message": "External checkout completed successfully (fallback)",
                                        "response_data": fb_data,
                                    }
                                else:
                                    err_msg = (
                                        (fb_data or {}).get("error") if isinstance(fb_data, dict) else None
                                    ) or "External checkout failed"
                                    return {"success": False, "message": err_msg, "response_data": fb_data}
                            else:
                                try:
                                    fb_text = await fb_resp.text()
                                except Exception:
                                    fb_text = ""
                                api_logger.log_response(
                                    context=fb_ctx,
                                    status_code=fb_status,
                                    status_message=fb_resp.reason or "Error",
                                    headers=fb_headers,
                                    body=fb_text,
                                    error_type="http_error",
                                    error_message=f"Status {fb_status}",
                                )
                                return {
                                    "success": False,
                                    "message": f"Checkout API error (fallback status {fb_status})",
                                }
                    except Exception as fb_e:
                        logger.error(f"Checkout fallback attempt failed: {fb_e}")
                return {
                    "success": False,
                    "message": f"Checkout API error (status {status})",
                }
        except asyncio.TimeoutError:
            logger.error("Timeout during external checkout")
            # Structured response log for timeout (synthetic entry)
            ctx = api_logger.create_context(user_id=None, operation="checkout")
            api_logger.log_response(
                context=ctx,
                status_code=408,
                status_message="Request Timeout",
                headers={},
                body=None,
                error_type="timeout",
                error_message="Checkout timeout",
            )
            return {"success": False, "message": "Checkout timeout - please try again"}
        except Exception as e:
            logger.error(f"Error executing external checkout: {e}")
            # Structured response log for exception (synthetic entry)
            ctx = api_logger.create_context(user_id=None, operation="checkout")
            api_logger.log_response(
                context=ctx,
                status_code=520,
                status_message="Exception",
                headers={},
                body=str(e),
                error_type="exception",
                error_message=str(e),
            )
            return {"success": False, "message": f"External checkout failed: {str(e)}"}

    async def _process_successful_checkout(
        self,
        user_id: str,
        cart_snapshot: Dict[str, Any],
        external_result: Dict[str, Any],
    ) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """Process successful external checkout in our local system"""
        try:
            async with database_transaction():
                items = cart_snapshot.get("items", [])
                total_amount = float(cart_snapshot.get("total_amount", 0.0))

                # Debit user wallet (idempotent reference)
                tx_metadata = {
                    "external_order_id": external_result.get("order_id"),
                    "checkout_timestamp": datetime.now(timezone.utc).isoformat(),
                    "items_count": len(items),
                }

                wallet, transaction = await self.user_service.debit_wallet(
                    user_id,
                    total_amount,
                    currency="USD",
                    reference=f"checkout_{external_result.get('order_id')}",
                    metadata=tx_metadata,
                    idempotency_key=f"checkout_{external_result.get('order_id')}",
                )

                # Create purchase records (idempotent per card)
                purchases_collection = get_collection("purchases")
                for item_data in items:
                    card_data = item_data.get("card_data", {})
                    card_pk = card_data.get("_id") or item_data.get("card_id")
                    qty = int(item_data.get("quantity", 1))
                    price = float(item_data.get("price_at_add", 0.0)) * qty

                    purchase = Purchase(
                        user_id=user_id,
                        sku=f"card_{card_pk}",
                        price=price,
                        currency="USD",
                        status=PurchaseStatus.SUCCESS,
                        metadata={
                            "card_id": card_pk,
                            "card_data": card_data,
                            "quantity": qty,
                            "external_order_id": external_result.get("order_id"),
                            "transaction_id": str(getattr(transaction, "id", "")),
                        },
                        idempotency_key=f"purchase_{external_result.get('order_id')}_{card_pk}",
                    )

                    try:
                        await purchases_collection.insert_one(purchase.to_mongo())
                    except Exception as pe:
                        # Likely duplicate due to idempotency
                        logger.debug(f"Purchase insert skipped (idempotent): {pe}")

                # Clear user's virtual cart
                await self.cart_service.clear_cart(user_id)

                result_data = {
                    "transaction_id": str(getattr(transaction, "id", "")),
                    "remaining_balance": getattr(wallet, "balance", None),
                }
                return (
                    True,
                    f"Successfully purchased {len(items)} items for ${total_amount}",
                    result_data,
                )

        except Exception as e:
            logger.error(f"Error processing successful checkout: {e}")
            return False, f"Failed to process checkout: {str(e)}", None

    async def _update_job_status(
        self,
        job_id: str,
        status: CheckoutJobStatus,
        metadata: Dict[str, Any] | None = None,
        error: str | None = None,
    ) -> None:
        """Update job status in database (merge metadata, keep datetimes as datetime)."""
        try:
            update_data: Dict[str, Any] = {
                "status": status.value,
                "updated_at": datetime.now(timezone.utc),
            }

            if status in [
                CheckoutJobStatus.COMPLETED,
                CheckoutJobStatus.FAILED,
                CheckoutJobStatus.CANCELLED,
            ]:
                update_data["completed_at"] = datetime.now(timezone.utc)

            if metadata:
                # Merge, don't overwrite completely if metadata already exists
                update_data["metadata"] = metadata

            if error:
                update_data["last_error"] = error

            await self.jobs_collection.update_one(
                {"job_id": job_id}, {"$set": update_data}
            )
        except Exception as e:
            logger.error(f"Error updating job status for {job_id}: {e}")

    async def _notify_user(
        self, job: CheckoutJob, message: str, result_data: Dict[str, Any] | None = None
    ) -> None:
        """Send notification to user via Telegram"""
        try:
            telegram_user_id = job.metadata.get("telegram_user_id")
            if not telegram_user_id:
                return

            # Import here to avoid circular imports
            from aiogram import Bot

            settings = get_settings()
            bot = Bot(token=settings.BOT_TOKEN)

            # Format message
            if result_data:
                formatted_message = self._format_completion_message(
                    message, result_data, job
                )
            else:
                formatted_message = (
                    f"🛒 <b>Order Update</b>\n\n{message}\n\n🆔 Job: {job.job_id[:8]}"
                )

            await bot.send_message(
                chat_id=telegram_user_id, text=formatted_message, parse_mode="HTML"
            )
            await bot.session.close()
        except Exception as e:
            logger.error(f"Error sending notification for job {job.job_id}: {e}")

    def _format_completion_message(
        self, message: str, result_data: Dict[str, Any], job: CheckoutJob
    ) -> str:
        """Format completion message with order details"""
        try:
            items = job.cart_snapshot.get("items", [])
            total_amount = result_data.get("total_amount", 0.0)
            remaining_balance = result_data.get("remaining_balance", 0.0)

            formatted_message = f"""
🎉 <b>Order Completed Successfully!</b>

📦 <b>Order Details:</b>
• Items: {len(items)} cards purchased
• Total: ${total_amount}
• Order ID: {result_data.get('external_order_id', 'N/A')}

💰 <b>Payment:</b>
• Transaction ID: {str(result_data.get('transaction_id', 'N/A'))[:8]}...
• Remaining Balance: ${remaining_balance}

🆔 Job: {job.job_id[:8]}
⏰ Completed: {datetime.now(timezone.utc).strftime('%H:%M:%S UTC')}
"""
            return formatted_message.strip()
        except Exception as e:
            logger.error(f"Error formatting completion message: {e}")
            return f"✅ {message}\n\n🆔 Job: {job.job_id[:8]}"

    async def _find_existing_job(
        self, user_id: str, cart_snapshot: Dict[str, Any]
    ) -> Optional[CheckoutJob]:
        """Find existing queued/processing job with the same stable cart fingerprint."""
        try:
            temp_job = CheckoutJob(user_id=user_id, cart_snapshot=cart_snapshot)
            job_doc = await self.jobs_collection.find_one(
                {
                    "user_id": user_id,
                    "idempotency_key": temp_job.idempotency_key,
                    "status": {
                        "$in": [
                            CheckoutJobStatus.QUEUED.value,
                            CheckoutJobStatus.PROCESSING.value,
                        ]
                    },
                }
            )
            return CheckoutJob.from_dict(job_doc) if job_doc else None
        except Exception as e:
            logger.error(f"Error finding existing job: {e}")
            return None

    async def _get_queue_position(self, job_id: str) -> int:
        """Get position of job in queue (1-based)."""
        try:
            job_doc = await self.jobs_collection.find_one({"job_id": job_id})
            if not job_doc:
                return 0
            created_at = job_doc["created_at"]
            # Count jobs created at or before this job which are still queued
            position = await self.jobs_collection.count_documents(
                {
                    "status": CheckoutJobStatus.QUEUED.value,
                    "created_at": {"$lte": created_at},
                }
            )
            return max(1, position)
        except Exception as e:
            logger.error(f"Error getting queue position: {e}")
            return 1

    async def _estimate_processing_time(self) -> int:
        """Rudimentary estimate: 30s per queued job."""
        try:
            queue_length = await self.jobs_collection.count_documents(
                {"status": CheckoutJobStatus.QUEUED.value}
            )
            return queue_length * 30
        except Exception as e:
            logger.error(f"Error estimating processing time: {e}")
            return 60

    async def get_queue_stats(self) -> Dict[str, Any]:
        """Get current queue statistics (counts and avg processing time of recent completions)."""
        try:
            stats: Dict[str, Any] = {}
            # Counts by status
            for status in CheckoutJobStatus:
                count = await self.jobs_collection.count_documents(
                    {"status": status.value}
                )
                stats[f"{status.value}_count"] = count

            # Average processing time over last 10 completed jobs
            completed_jobs = await (
                self.jobs_collection.find(
                    {
                        "status": CheckoutJobStatus.COMPLETED.value,
                        "completed_at": {"$exists": True},
                        "created_at": {"$exists": True},
                    }
                )
                .sort("completed_at", -1)
                .limit(10)
                .to_list(None)
            )

            processing_times: List[float] = []
            for job in completed_jobs:
                created_at = job.get("created_at")
                completed_at = job.get("completed_at")
                if isinstance(created_at, datetime) and isinstance(
                    completed_at, datetime
                ):
                    delta = (completed_at - created_at).total_seconds()
                    if delta >= 0:
                        processing_times.append(delta)

            if processing_times:
                stats["avg_processing_time"] = sum(processing_times) / len(
                    processing_times
                )

            return stats
        except Exception as e:
            logger.error(f"Error getting queue stats: {e}")
            return {}
